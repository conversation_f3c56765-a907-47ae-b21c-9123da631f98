"""
SDS6108 H10 Pro Oscilloscope Driver (PyMeasure-compatible)

本驱动遵循 PyMeasure 新仪器规范（Instrument.control / Instrument.measurement 等描述符），
实现典型示波器功能：运行/停止/单次、自动设置、通道设置、时基设置、触发设置、
波形读取（ASCII 方式，避免二进制块兼容性差异）、基础测量与屏幕截图。

说明
- 该型号命令集参考常见 SDS 系列 SCPI 结构（与 Siglent/Rigol/Keysight 命令集相近），
  实际指令名称可能存在差异，请结合实际设备的远程控制手册适当调整。
- 波形获取默认使用 ASCII 方式（WAVEFORM:FORMAT ASCII），以提升兼容性并简化适配；
  若需要高速采样，可补充二进制块读取逻辑（WAVEFORM:FORMAT BYTE + 解析 SCPI 块）。

示例（单位严格说明）
    from instrument.sds6108_h10_pro import SDS6108H10Pro

    # 连接（VISA 资源字符串），查询标识
    scope = SDS6108H10Pro("TCPIP::************::INSTR")
    print(scope.idn)

    # 运行/自动设置
    scope.run()
    scope.autoset()

    # 通道 1 配置（在 channels[1] 上操作）
    ch1 = scope.channels[1]
    ch1.display = True                 # 显示通道轨迹（布尔）
    ch1.scale = 0.5                    # 垂直刻度：0.5 V/div（单位：V/格）
    ch1.offset = 0.0                   # 垂直偏置：0.0 V（单位：V）
    ch1.coupling = "DC"                # 耦合方式：DC/AC/GND
    ch1.probe = 10                     # 探头倍率：10（单位：倍）
    ch1.invert = False                 # 极性翻转：否

    # 时基设置（仪器级属性）
    scope.timebase_scale = 1e-3        # 水平刻度：1e-3 s/div（单位：秒/格）
    scope.timebase_offset = 0.0        # 水平偏置：0.0 s（单位：秒）

    # 触发设置（仪器级属性）
    scope.trigger_mode = "EDGE"        # 触发模式：EDGE（边沿）
    scope.trigger_source = "C1"        # 触发源：通道1（C1）
    scope.trigger_level = 0.0          # 触发电平：0.0 V（单位：V）
    scope.trigger_slope = "POS"        # 触发边沿：正沿 POS/NEG/EITH
    scope.trigger_sweep = "AUTO"       # 扫掠：AUTO/NORM/SING

    # 采集波形（ASCII）
    # 获取屏幕“所有数据”：NORM + MAX（推荐）
    t, v = scope.fetch_waveform_ascii(1, mode="NORM", points="MAX")
    # 获取完整记录深度（数据量更大）：RAW + MAX
    t_raw, v_raw = scope.fetch_waveform_ascii(1, mode="RAW", points="MAX")
    # t/t_raw 单位：秒（s）；v/v_raw 单位：伏特（V）

    # 便捷方法（等价于上述两种调用）
    t2, v2 = scope.fetch_waveform_screen(1)  # 屏幕数据
    t3, v3 = scope.fetch_waveform_raw(1)     # 记录深度数据

    # 常用测量（通道级 measurement 属性，单位见注释）
    print("Vpp:", ch1.vpp)             # 峰-峰值（单位：V）
    print("Vrms:", ch1.vrms)           # 有效值（单位：V）
    print("Freq:", ch1.frequency)      # 频率（单位：Hz）
    print("Period:", ch1.period)       # 周期（单位：s）

    # 屏幕截图
    scope.screenshot_png("screen.png")

    # 关断
    scope.shutdown()
"""

from __future__ import annotations

from typing import List, Tuple, Optional

import time
import math

import numpy as np
from pymeasure.instruments import Instrument, Channel


class SDS6108H10ProChannel(Channel):
    """
    SDS6000 系列通道抽象（C{ch}）。

    注意：命令基于 SDS6000 Pro 手册常见形式，若固件存在差异，请按手册调整关键字。
    """

    # 显示/隐藏通道轨迹
    display = Instrument.control(
        "C{ch}:DISP?", "C{ch}:DISP %s",
        "通道显示控制（True=ON/False=OFF）",
        map_values=True,
        values={True: "ON", False: "OFF"},
    )

    # 垂直刻度与偏置（V/div, V）
    scale = Instrument.control(
        "C{ch}:SCALe?", "C{ch}:SCALe %e",
        "垂直刻度（V/div）",
        get_process=float,
        set_process=float,
    )

    offset = Instrument.control(
        "C{ch}:OFFSet?", "C{ch}:OFFSet %e",
        "垂直偏置（V）",
        get_process=float,
        set_process=float,
    )

    # 耦合方式
    coupling = Instrument.control(
        "C{ch}:COUPling?", "C{ch}:COUPling %s",
        "耦合方式",
        map_values=True,
        values={"DC": "DC", "AC": "AC", "GND": "GND"},
    )

    # 带宽限制
    bandwidth_limit = Instrument.control(
        "C{ch}:BWL?", "C{ch}:BWL %s",
        "带宽限制（例如 OFF/20M/FULL，依固件而定）",
    )

    # 探头倍率
    probe = Instrument.control(
        "C{ch}:PROBe?", "C{ch}:PROBe %g",
        "探头倍率（1/10/100 等）",
        get_process=float,
        set_process=float,
    )

    # 极性翻转
    invert = Instrument.control(
        "C{ch}:INVert?", "C{ch}:INVert %s",
        "极性翻转（True=ON/False=OFF）",
        map_values=True,
        values={True: "ON", False: "OFF"},
    )

    # 常用测量（按 Siglent 通用风格）
    vpp = Instrument.measurement(
        "MEASure:ITEM? VPP,C{ch}",
        "峰-峰值（V）",
        get_process=float,
    )

    vrms = Instrument.measurement(
        "MEASure:ITEM? VRMS,C{ch}",
        "有效值（V）",
        get_process=float,
    )

    frequency = Instrument.measurement(
        "MEASure:ITEM? FREQ,C{ch}",
        "频率（Hz）",
        get_process=float,
    )

    period = Instrument.measurement(
        "MEASure:ITEM? PER,C{ch}",
        "周期（s）",
        get_process=float,
    )


class SDS6108H10Pro(Instrument):
    """
    SDS6108 H10 Pro 示波器驱动

    参数
    - adapter: PyMeasure 适配器（VISA 字符串或自定义适配器）
    - name: 仪器名称
    - kwargs: 传递给 Instrument 父类的其他关键字参数

    约定
    - 默认以换行符作为读/写终止符（大多数示波器均如此）。
    - 面向 SCPI 的字符串命令，优先采用通用命令名称；若设备不兼容，请在本驱动上方注释处做适配。
    """

    def __init__(self, adapter, name: str = "SDS6108 H10 Pro Oscilloscope", **kwargs):
        """
        初始化示波器。

        适配简化的 IP 直连：
        - 如果 adapter 为类似 "************" 或主机名（不含 "::"），自动转换为
          "TCPIP::<adapter>::INSTR" 以兼容 VISA 资源格式。
        - 若传入完整 VISA 资源字符串（含 "::"），则按原样使用。
        """
        kwargs.setdefault("write_termination", "\n")
        kwargs.setdefault("read_termination", "\n")

        normalized = adapter
        try:
            if isinstance(adapter, str) and "::" not in adapter:
                # 仅 IP/主机名，自动补全为 VISA 资源
                normalized = f"TCPIP::{adapter}::INSTR"
        except Exception:
            normalized = adapter

        super().__init__(normalized, name, **kwargs)

    # 多通道创建（SDS6108 为 8 通道）
    channels = Instrument.MultiChannelCreator(SDS6108H10ProChannel, list(range(1, 9)))

    # ------------------------------
    # 基础识别与状态
    # ------------------------------
    idn = Instrument.measurement(
        "*IDN?",
        "返回设备标识字符串",
    )

    @property
    def is_running(self) -> bool:
        """判定是否处于运行采集状态（基于触发状态查询）。"""
        try:
            # 兼容：TRIGger:STATus? 或 STATus:OPERation? 等
            status = str(self.ask("TRIGger:STATus?")).strip().upper()
            # 常见返回：RUN, STOP, TD, AUTO 等；做一个宽松判断
            return status in {"RUN", "AUTO", "TD", "READY"}
        except Exception:
            return False

    # ------------------------------
    # 运行控制
    # ------------------------------
    def run(self) -> None:
        """连续运行采集。"""
        self.write("RUN")

    def stop(self) -> None:
        """停止采集。"""
        self.write("STOP")

    def single(self) -> None:
        """单次触发采集。"""
        # 部分设备使用 SINGle 或 SINGLE
        self.write("SINGle")

    def autoset(self) -> None:
        """自动设置（示波器自适应）。"""
        self.write("AUTO")

    # ------------------------------
    # 通道设置
    # ------------------------------
    # 使用通道属性（channels[1].display/scale/...）来配置通道，避免冗余的 set_channel 方法。

    # ------------------------------
    # 时基设置
    # ------------------------------
    # 时基（s/div 与 偏置）
    timebase_scale = Instrument.control(
        "TIMebase:SCALe?", "TIMebase:SCALe %e",
        "时基刻度（s/div）",
        get_process=float,
        set_process=float,
    )

    timebase_offset = Instrument.control(
        "TIMebase:OFFSet?", "TIMebase:OFFSet %e",
        "时基偏置（s）",
        get_process=float,
        set_process=float,
    )

    # ------------------------------
    # 触发设置
    # ------------------------------
    # 触发相关（采用通用 SCPI 形态）
    trigger_mode = Instrument.control(
        "TRIGger:MODE?", "TRIGger:MODE %s",
        "触发模式（EDGE/VIDEO/PULSE 等，依设备支持）",
        map_values=True,
        values={"EDGE": "EDGE", "VIDEO": "VIDEO", "PULSE": "PULSE"},
    )

    trigger_source = Instrument.control(
        "TRIGger:SOURCE?", "TRIGger:SOURCE %s",
        "触发源（C1/C2/.../EXT）",
    )

    trigger_level = Instrument.control(
        "TRIGger:LEVel?", "TRIGger:LEVel %e",
        "触发电平（V）",
        get_process=float,
        set_process=float,
    )

    trigger_slope = Instrument.control(
        "TRIGger:EDGE:SLOPe?", "TRIGger:EDGE:SLOPe %s",
        "边沿触发方向（POS/NEG/EITH）",
        map_values=True,
        values={"POS": "POS", "NEG": "NEG", "EITH": "EITH"},
    )

    trigger_sweep = Instrument.control(
        "TRIGger:SWEep?", "TRIGger:SWEep %s",
        "触发扫掠（AUTO/NORM/SING）",
        map_values=True,
        values={"AUTO": "AUTO", "NORM": "NORM", "SING": "SING"},
    )

    # ------------------------------
    # 波形获取（ASCII）
    # ------------------------------
    def _configure_waveform_query(self, ch: int, *, mode: str = "NORM", points: Optional[object] = "MAX") -> int:
        """
        内部：设置波形查询上下文（ASCII 格式），返回设备确认的实际点数。

        参数
        - ch: 通道号
        - mode: "NORM"（屏幕等效点）或 "RAW"（完整记录深度）
        - points: 整数点数或字符串 "MAX"（推荐）
        """
        self.write(f"WAVEFORM:SOURCE C{int(ch)}")
        self.write("WAVEFORM:FORMAT ASCII")

        mode = str(mode).upper()
        if mode not in {"NORM", "RAW"}:
            raise ValueError("mode 仅支持 'NORM' 或 'RAW'")
        # 设置点模式
        self.write(f"WAVEFORM:POINTS:MODE {mode}")

        # 设置点数（推荐 MAX）
        if points is not None:
            if isinstance(points, str) and points.upper() == "MAX":
                self.write("WAVEFORM:POINTS MAX")
            else:
                self.write(f"WAVEFORM:POINTS {int(points)}")

        # 查询设备确认的实际点数
        try:
            actual = int(self.ask("WAVEFORM:POINTS?"))
        except Exception:
            actual = int(points) if isinstance(points, int) else 0
        return actual

    def _read_preamble(self) -> Tuple[float, float, float, float, float, float]:
        """
        读取并解析预置信息，返回 (xinc, xorig, xref, yinc, yorig, yref)。

        兼容 Keysight/Rigol/Siglent 通用 PREAMBLE 结构：
        FORMAT, TYPE, POINTS, COUNT, XINC, XORIG, XREF, YINC, YORIG, YREF
        """
        pre = self.ask("WAVEFORM:PREAMBLE?")
        parts = [p.strip() for p in pre.split(',')]
        if len(parts) < 10:
            # 某些设备返回空格分隔或不同顺序，尽量宽松解析
            parts = [p for p in pre.replace(";", ",").split(',')]
        if len(parts) < 10:
            raise ValueError(f"无法解析 PREAMBLE（返回长度不足）: {pre}")

        # 取后 6 个为缩放参数
        xinc = float(parts[-6])
        xorig = float(parts[-5])
        xref = float(parts[-4])
        yinc = float(parts[-3])
        yorig = float(parts[-2])
        yref = float(parts[-1])
        return xinc, xorig, xref, yinc, yorig, yref

    def fetch_waveform_ascii(self, ch: int, *, mode: str = "NORM", points: Optional[object] = "MAX") -> Tuple[np.ndarray, np.ndarray]:
        """
        以 ASCII 格式获取波形数据，返回 (t, v)。

        - ch: 通道号
        - mode: "NORM"（屏幕等效点）或 "RAW"（完整记录深度）
        - points: 整数或 "MAX"（推荐）。默认 (NORM, MAX) 即“屏幕所有数据”。
        """
        self._configure_waveform_query(ch, mode=mode, points=points)
        xinc, xorig, xref, yinc, yorig, yref = self._read_preamble()

        # 读取 ASCII 数据
        raw = self.ask("WAVEFORM:DATA?")
        # 数据一般形如 "1.23,1.24,..." 或以花括号/前缀开头，尽量宽松处理
        raw = raw.strip()
        if raw.startswith("{") and raw.endswith("}"):
            raw = raw[1:-1]
        str_vals = [s for s in raw.replace(";", ",").split(',') if s.strip()]
        y_vals = np.array([float(s) for s in str_vals], dtype=float)

        # 根据 PREAMBLE 做一次线性缩放（若设备直接返回物理量，可近似视为 y_vals 即电压）
        if not np.isnan(yinc) and not np.isnan(yref) and not np.isnan(yorig):
            y = (y_vals - yref) * yinc + yorig
        else:
            y = y_vals

        # 构造时间轴
        n = len(y)
        t = xorig + (np.arange(n) - xref) * xinc
        return t, y

    def fetch_waveform_screen(self, ch: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取屏幕“所有数据”（等效屏幕像素数）：等价于 mode="NORM", points="MAX"。
        返回 (t, v)，单位分别为秒(s)与伏特(V)。
        """
        return self.fetch_waveform_ascii(ch, mode="NORM", points="MAX")

    def fetch_waveform_raw(self, ch: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取完整记录深度的“所有数据”：等价于 mode="RAW", points="MAX"。
        返回 (t, v)，单位分别为秒(s)与伏特(V)。
        """
        return self.fetch_waveform_ascii(ch, mode="RAW", points="MAX")

    # ------------------------------
    # 测量（常用几项）
    # ------------------------------
    def _measure_item(self, item: str, ch: int) -> float:
        """通用测量查询。部分品牌使用 "MEASure:ITEM? <ITEM>,CHANnel1"。"""
        try:
            val = float(self.ask(f"MEASure:ITEM? {item},C{int(ch)}"))
            return val
        except Exception:
            # 备选：MEAS:<ITEM>? CHAN1 或 MEAS:VPP? CHAN1
            return float(self.ask(f"MEASure:{item}? C{int(ch)}"))

    def measure_vpp(self, ch: int) -> float:
        return self._measure_item("VPP", ch)

    def measure_vrms(self, ch: int) -> float:
        return self._measure_item("VRMS", ch)

    def measure_frequency(self, ch: int) -> float:
        return self._measure_item("FREQ", ch)

    def measure_period(self, ch: int) -> float:
        return self._measure_item("PER", ch)

    # ------------------------------
    # 屏幕截图（可能因型号命令不同需要调整）
    # ------------------------------
    def screenshot_png(self, filepath: str) -> None:
        """
        获取屏幕截图为 PNG 并保存到 filepath。

        注意：不同品牌命令存在差异，下面命令为常见组合：
        - RIGOL/KEYSIGHT 常见：DISPlay:DATA? PNG,SCReen,ON
        - SIGLENT 常见：DISPlay:DATA? ON,0,PNG
        如遇不兼容，请根据设备手册修改。
        """
        try:
            # 优先尝试 Siglent 风格
            self.write("DISPlay:DATA? ON,0,PNG")
            raw = self.read_bytes(size=None)  # 期望底层适配器可读取 SCPI 块
        except Exception:
            # 回退到 Rigol/Keysight 风格
            raw = self.ask_raw("DISPlay:DATA? PNG,SCReen,ON")

        # 将二进制内容写入文件
        if isinstance(raw, (bytes, bytearray)):
            data = raw
        else:
            # 某些适配器返回 str，需要转为 bytes（可能已是 Base64/HEX，此处假设直出二进制被错误解码）
            data = bytes(raw, "latin1")  # 尽量无损

        with open(filepath, "wb") as f:
            f.write(data)

    # ------------------------------
    # 安全关断
    # ------------------------------
    def shutdown(self) -> None:
        """安全关断：停止采集，调用父类 shutdown。"""
        try:
            self.stop()
            time.sleep(0.05)
        finally:
            super().shutdown()



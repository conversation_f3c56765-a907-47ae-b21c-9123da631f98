"""
Test script to verify that the voltage property recursion issue is fixed.

This script specifically tests that setting voltage no longer causes infinite recursion.
"""

import sys
from unittest.mock import Mock

# Mock PyMeasure for testing without actual hardware
sys.modules['pymeasure'] = Mock()
sys.modules['pymeasure.instruments'] = Mock()

class MockInstrument:
    def __init__(self, adapter, name="Mock Instrument", **kwargs):
        self.adapter = adapter
        self.name = name
        self.kwargs = kwargs
        self._commands = []
        self._responses = {}

    def write(self, command):
        self._commands.append(command)
        print(f"WRITE: {command}")

    def ask(self, command):
        self._commands.append(command)
        response = self._responses.get(command, "5.0")  # Default to 5V
        print(f"ASK: {command} -> {response}")
        return response

    def set_response(self, command, response):
        self._responses[command] = response

    @staticmethod
    def control(get_command, set_command, doc="", set_process=None, **kwargs):
        """Mock control property creator."""
        def getter(self):
            return float(self.ask(get_command.format(ch=self.id)))

        def setter(self, value):
            if set_process:
                # Call the custom set_process function
                set_process(self, value)
            else:
                self.write(set_command.format(ch=self.id) % value)

        return property(getter, setter)

    @staticmethod
    def measurement(command, doc="", **kwargs):
        """Mock measurement property creator."""
        def getter(self):
            return float(self.ask(command.format(ch=self.id)))

        return property(getter)

class MockChannel:
    def __init__(self, parent, id, **kwargs):
        self.parent = parent
        self.id = id
        self.kwargs = kwargs
    
    def write(self, command):
        return self.parent.write(command)
    
    def ask(self, command):
        return self.parent.ask(command)

def MockMultiChannelCreator(channel_class, channel_ids):
    """Mock multi-channel creator."""
    class ChannelCollection:
        def __init__(self, instrument):
            self._channels = {}
            for ch_id in channel_ids:
                self._channels[ch_id] = channel_class(instrument, ch_id)

        def __getitem__(self, key):
            return self._channels[key]

        def values(self):
            return self._channels.values()

    return ChannelCollection

# Set up mocks
sys.modules['pymeasure.instruments'].Instrument = MockInstrument
sys.modules['pymeasure.instruments'].Channel = MockChannel
MockInstrument.MultiChannelCreator = MockMultiChannelCreator

# Now import our driver
from instrument.siglent_spd import SiglentSPD4000X

def test_no_recursion():
    """Test that voltage setting doesn't cause recursion."""
    print("=== Testing Voltage Recursion Fix ===")

    # Create a mock channel directly
    from instrument.siglent_spd import SiglentSPD4000XChannel

    # Create mock parent
    mock_parent = MockInstrument("mock://test")
    mock_parent.set_response("MEASure:VOLTage? CH1", "2.0")  # Current voltage is 2V

    # Create channel
    ch1 = SiglentSPD4000XChannel(mock_parent, 1)

    print("Setting voltage from 2V to 8V...")
    print("This should NOT cause infinite recursion!")

    try:
        # This should work without recursion
        ch1.voltage = 8.0
        print("✓ Voltage setting completed successfully - no recursion!")

        # Verify the gradual transition was used
        voltage_writes = [cmd for cmd in mock_parent._commands if "CH1:VOLTage" in cmd and "?" not in cmd]

        print(f"Number of voltage write commands: {len(voltage_writes)}")
        if len(voltage_writes) > 1:
            print("✓ Gradual voltage transition was used")
        else:
            print("✓ Direct voltage setting was used")

    except RecursionError:
        print("✗ FAILED: Infinite recursion detected!")
        raise
    except Exception as e:
        print(f"✗ FAILED: Unexpected error: {e}")
        raise

def test_shutdown_no_recursion():
    """Test that shutdown doesn't cause recursion."""
    print("\n=== Testing Shutdown Recursion Fix ===")

    # Create a mock channel directly
    from instrument.siglent_spd import SiglentSPD4000XChannel

    # Create mock parent
    mock_parent = MockInstrument("mock://test")
    mock_parent.set_response("MEASure:VOLTage? CH1", "10.0")  # Current voltage is 10V

    # Create channel
    ch1 = SiglentSPD4000XChannel(mock_parent, 1)

    print("Testing shutdown with 10V current voltage...")
    print("This should NOT cause infinite recursion!")

    try:
        # This should work without recursion
        ch1.shutdown()
        print("✓ Shutdown completed successfully - no recursion!")

    except RecursionError:
        print("✗ FAILED: Infinite recursion detected in shutdown!")
        raise
    except Exception as e:
        print(f"✗ FAILED: Unexpected error in shutdown: {e}")
        raise

def main():
    """Run recursion tests."""
    print("Testing Voltage Property Recursion Fix")
    print("=" * 50)
    
    try:
        test_no_recursion()
        test_shutdown_no_recursion()
        
        print("\n" + "=" * 50)
        print("✓ All recursion tests passed!")
        print("The voltage property recursion issue has been fixed.")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

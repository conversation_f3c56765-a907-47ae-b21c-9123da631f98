# Core scientific
numpy

# PyMeasure framework
pymeasure>=0.12.0

# Instrument communication (VISA over TCP/IP)
pyvisa>=1.13.0
pyvisa-py>=0.7.0

# Ocean Optics / SeaBreeze spectrometer
seabreeze>=2.3.0
pyusb>=1.2.1

# Toptica CTL laser SDK
toptica-lasersdk>=3.0.0

# GUI backend used by PyMeasure ManagedWindow (choose one; PyQt5 by default)
PyQt5>=5.15.0
# Alternatively:
# PySide6>=6.5.0

# Testing (optional)
# pytest>=7.0

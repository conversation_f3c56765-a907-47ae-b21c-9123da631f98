"""
Test script to verify the Toptica laser emission control fix.

This script tests that the emission_enabled property now uses the correct
DeCoP command (laser1:dl:cc:enabled) instead of the problematic laser1:emission.
"""

import sys
from unittest.mock import Mock

# Mock the Toptica SDK for testing without actual hardware
sys.modules['toptica'] = Mock()
sys.modules['toptica.lasersdk'] = Mock()
sys.modules['toptica.lasersdk.client'] = Mock()
sys.modules['toptica.lasersdk.decop'] = Mock()

# Create mock classes
class MockClient:
    def __init__(self, connection):
        self.connection = connection
        self._values = {
            "laser1:dl:cc:enabled": "false",
            "laser1:emission": "false",
            "laser1:ctl:wavelength-act": "1550.0",
            "laser1:dl:cc:current-act": "100.0",
            "laser1:dl:tc:temp-act": "25.0",
            "laser1:dl:pc:power-act": "10.0",
            "serial-number": "TEST123",
            "system-health-txt": "OK"
        }

    def get(self, command):
        return self._values.get(command, "0")

    def set(self, command, value):
        self._values[command] = str(value)
        print(f"SET: {command} = {value}")

class MockUserLevel:
    NORMAL = 3
    MAINTENANCE = 2
    SERVICE = 1

class MockDecopError(Exception):
    pass

# Set up the mocks
sys.modules['toptica.lasersdk.client'].Client = MockClient
sys.modules['toptica.lasersdk.decop'].UserLevel = MockUserLevel
sys.modules['toptica.lasersdk.decop'].DecopError = MockDecopError

# Now import our driver
from instrument.toptica_ctl import TopticaCTL

def test_emission_control_fix():
    """Test that emission control now uses the correct DeCoP commands."""
    print("=== Testing Emission Control Fix ===")
    
    # Create a mock laser instance
    laser = TopticaCTL.__new__(TopticaCTL)  # Create without calling __init__
    
    # Set up mock client
    laser.client = MockClient(None)
    
    # Override read and write methods to use our mock
    def mock_read(command):
        return laser.client.get(command)
    
    def mock_write(command):
        # Parse command like "laser1:dl:cc:enabled true"
        parts = command.split()
        if len(parts) == 2:
            param, value = parts
            laser.client.set(param, value)
        else:
            print(f"WRITE: {command}")
    
    laser.read = mock_read
    laser.write = mock_write
    
    print("Testing emission control...")
    
    # Test reading emission state (should be False initially)
    emission_state = laser.emission_enabled
    print(f"Initial emission state: {emission_state}")
    assert emission_state == False, "Initial emission state should be False"
    
    # Test enabling emission
    print("Enabling emission...")
    laser.emission_enabled = True
    
    # Verify the correct command was sent
    enabled_value = laser.client.get("laser1:dl:cc:enabled")
    print(f"laser1:dl:cc:enabled value after enabling: {enabled_value}")
    assert enabled_value == "true", "laser1:dl:cc:enabled should be 'true' after enabling"
    
    # Test reading emission state after enabling
    emission_state = laser.emission_enabled
    print(f"Emission state after enabling: {emission_state}")
    assert emission_state == True, "Emission state should be True after enabling"
    
    # Test disabling emission
    print("Disabling emission...")
    laser.emission_enabled = False
    
    # Verify the correct command was sent
    enabled_value = laser.client.get("laser1:dl:cc:enabled")
    print(f"laser1:dl:cc:enabled value after disabling: {enabled_value}")
    assert enabled_value == "false", "laser1:dl:cc:enabled should be 'false' after disabling"
    
    # Test reading emission state after disabling
    emission_state = laser.emission_enabled
    print(f"Emission state after disabling: {emission_state}")
    assert emission_state == False, "Emission state should be False after disabling"
    
    print("✓ Emission control fix working correctly!")

def test_emission_status_property():
    """Test the new read-only emission_status property."""
    print("\n=== Testing Emission Status Property ===")
    
    # Create a mock laser instance
    laser = TopticaCTL.__new__(TopticaCTL)
    laser.client = MockClient(None)
    
    def mock_read(command):
        return laser.client.get(command)
    
    laser.read = mock_read
    
    # Test reading emission status (should read from laser1:emission)
    laser.client._values["laser1:emission"] = "false"
    status = laser.emission_status
    print(f"Emission status (laser1:emission = false): {status}")
    assert status == False, "Emission status should be False when laser1:emission is false"
    
    # Test with emission on
    laser.client._values["laser1:emission"] = "true"
    status = laser.emission_status
    print(f"Emission status (laser1:emission = true): {status}")
    assert status == True, "Emission status should be True when laser1:emission is true"
    
    print("✓ Emission status property working correctly!")

def test_no_parameter_not_settable_error():
    """Test that we no longer get 'parameter not settable' errors."""
    print("\n=== Testing No Parameter Not Settable Error ===")
    
    # Create a mock laser instance
    laser = TopticaCTL.__new__(TopticaCTL)
    laser.client = MockClient(None)
    
    def mock_read(command):
        return laser.client.get(command)
    
    def mock_write(command):
        # Simulate the old error for laser1:emission
        if "laser1:emission" in command:
            raise MockDecopError("Error: -11 parameter not settable")
        
        # But allow laser1:dl:cc:enabled
        parts = command.split()
        if len(parts) == 2:
            param, value = parts
            laser.client.set(param, value)
    
    laser.read = mock_read
    laser.write = mock_write
    
    try:
        # This should work without error now
        laser.emission_enabled = True
        print("✓ No 'parameter not settable' error when setting emission_enabled = True")
        
        laser.emission_enabled = False
        print("✓ No 'parameter not settable' error when setting emission_enabled = False")
        
    except MockDecopError as e:
        print(f"✗ Still getting error: {e}")
        raise
    
    print("✓ Parameter not settable error has been fixed!")

def main():
    """Run all tests."""
    print("Testing Toptica Laser Emission Control Fix")
    print("=" * 50)
    
    try:
        test_emission_control_fix()
        test_emission_status_property()
        test_no_parameter_not_settable_error()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed!")
        print("\nKey improvements:")
        print("- emission_enabled now uses laser1:dl:cc:enabled (settable)")
        print("- Added emission_status property for laser1:emission (read-only)")
        print("- Fixed 'Error: -11 parameter not settable' issue")
        print("- Maintains PyMeasure standards and existing API")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

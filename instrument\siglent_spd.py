"""
Siglent SPD4000X Series Power Supply Driver

This module provides a PyMeasure driver for Siglent SPD4000X series power supplies
with proper multi-channel support using PyMeasure's Channel architecture.

The SPD4000X series has 4 channels with different voltage and current ranges:
- CH1: 0-15V, 0-1.5A (High precision channel)
- CH2: 0-12V, 0-10A (High current channel)
- CH3: 0-12V, 0-10A (High current channel)
- CH4: 0-15V, 0-1.5A (High precision channel)
"""

import time
from pymeasure.instruments import Instrument, Channel


class SiglentSPD4000XChannel(Channel):
    """
    Represents a single channel of the Siglent SPD4000X power supply.

    This class implements the individual channel controls for voltage, current,
    and output state using PyMeasure's Channel base class for proper multi-channel support.
    Each channel has different voltage and current ranges based on its type.
    """

    def __init__(self, parent, id, **kwargs):
        """
        Initialize a channel with channel-specific voltage and current ranges.

        Args:
            parent: The parent instrument instance
            id: Channel identifier (1, 2, 3, or 4)
            **kwargs: Additional keyword arguments
        """
        super().__init__(parent, id, **kwargs)

        # Set channel-specific ranges based on SPD4000X specifications
        if id in [1, 4]:  # High precision channels
            self._voltage_range = (0, 15.0)
            self._current_range = (0, 1.5)
        elif id in [2, 3]:  # High current channels
            self._voltage_range = (0, 12.0)
            self._current_range = (0, 10.0)
        else:
            raise ValueError(f"Invalid channel ID: {id}. Must be 1, 2, 3, or 4.")

    def _set_voltage_with_transition(self, value):
        """
        Set the output voltage of the channel with smooth transitions.

        Args:
            value (float): Target voltage in Volts
        """
        # Validate range
        if not (self._voltage_range[0] <= value <= self._voltage_range[1]):
            raise ValueError(f"Voltage {value}V is outside valid range "
                           f"{self._voltage_range[0]}-{self._voltage_range[1]}V for channel {self.id}")

        # Get current voltage for smooth transition using measure_voltage to avoid recursion
        current_voltage = self.measure_voltage

        # If the change is significant, implement gradual transition
        voltage_diff = abs(value - current_voltage)
        if voltage_diff > 1.0:  # Only for changes > 1V
            # Calculate number of steps (max 0.5V per step)
            num_steps = max(int(voltage_diff / 0.5), 1)
            step_size = (value - current_voltage) / num_steps

            for i in range(num_steps):
                intermediate_voltage = current_voltage + (i + 1) * step_size
                self.write(f"CH{self.id}:VOLTage {intermediate_voltage:.3f}")
                time.sleep(0.1)  # Small delay between steps
        else:
            # Direct setting for small changes
            self.write(f"CH{self.id}:VOLTage {value:.3f}")

    @property
    def voltage(self):
        """Get the output voltage of the channel in Volts."""
        return float(self.ask(f"CH{self.id}:VOLTage?"))

    @voltage.setter
    def voltage(self, value):
        """
        Set the output voltage of the channel in Volts.

        Supports smooth voltage transitions for large changes (>1V) to prevent
        damage to connected devices. Changes are made gradually in 0.5V steps
        with 0.1s delays between steps.
        """
        self._set_voltage_with_transition(value)

    @property
    def current_limit(self):
        """Get the current limit of the channel in Amperes."""
        return float(self.ask(f"CH{self.id}:CURRent?"))

    @current_limit.setter
    def current_limit(self, value):
        """
        Set the current limit of the channel in Amperes.

        Args:
            value (float): Current limit in Amperes
        """
        # Validate range
        if not (self._current_range[0] <= value <= self._current_range[1]):
            raise ValueError(f"Current {value}A is outside valid range "
                           f"{self._current_range[0]}-{self._current_range[1]}A for channel {self.id}")

        self.write(f"CH{self.id}:CURRent {value:.3f}")

    measure_voltage = Instrument.measurement(
        "MEASure:VOLTage? CH{ch}",
        """Measure the actual output voltage of the channel in Volts."""
    )

    measure_current = Instrument.measurement(
        "MEASure:CURRent? CH{ch}",
        """Measure the actual output current of the channel in Amperes."""
    )

    output_enabled = Instrument.control(
        "OUTPut? CH{ch}", "OUTPut CH{ch},%s",
        """Control the output state of the channel.

        True enables the output, False disables it.
        """,
        map_values=True,
        values={True: 'ON', False: 'OFF'}
    )

    def shutdown(self):
        """
        Safely shutdown this channel by gradually reducing voltage to 0 and disabling output.
        """
        # Gradually reduce voltage to prevent damage to connected devices
        current_voltage = self.measure_voltage  # Use measure_voltage to avoid recursion
        if current_voltage > 0.1:  # Only if voltage is significant
            # Reduce voltage in steps
            num_steps = max(int(current_voltage / 0.5), 1)
            for i in range(num_steps):
                step_voltage = current_voltage * (1 - (i + 1) / num_steps)
                self.voltage = step_voltage
                time.sleep(0.1)

        # Final set to 0 and disable output
        self.voltage = 0
        self.output_enabled = False


class SiglentSPD4000X(Instrument):
    """
    Siglent SPD4000X Series Power Supply Driver

    This class provides a PyMeasure driver for Siglent SPD4000X series power supplies
    with proper multi-channel support. The SPD4000X series has 4 channels with different
    voltage and current ranges:

    - CH1: 0-15V, 0-1.5A (High precision channel)
    - CH2: 0-12V, 0-10A (High current channel)
    - CH3: 0-12V, 0-10A (High current channel)
    - CH4: 0-15V, 0-1.5A (High precision channel)

    Example usage:
        # Connect to the power supply
        psu = SiglentSPD4000X("TCPIP::*************::INSTR")

        # Access individual channels with proper ranges
        psu.ch_1.voltage = 5.0      # High precision channel (0-15V)
        psu.ch_1.current_limit = 1.0  # Max 1.5A
        psu.ch_1.output_enabled = True

        psu.ch_2.voltage = 12.0     # High current channel (0-12V)
        psu.ch_2.current_limit = 5.0  # Max 10A
        psu.ch_2.output_enabled = True

        # Read measurements
        actual_voltage = psu.ch_1.measure_voltage
        actual_current = psu.ch_1.measure_current

        # Access channels through collection
        psu.channels[1].voltage = 3.3
        psu.channels[2].voltage = 12.0

        # Shutdown all channels safely with gradual voltage reduction
        psu.shutdown()
    """

    def __init__(self, adapter, name="Siglent SPD4000X Power Supply", **kwargs):
        """
        Initialize the Siglent SPD4000X power supply.

        Args:
            adapter: PyMeasure adapter for communication (VISA resource string or adapter object)
            name: Name of the instrument
            **kwargs: Additional keyword arguments passed to parent Instrument class
        """
        # Set default termination characters for Siglent instruments
        kwargs.setdefault('write_termination', '\n')
        kwargs.setdefault('read_termination', '\n')

        # PyMeasure 0.13+ deprecates includeSCPI=True default; silence by disabling SCPI header in IDN
        kwargs.setdefault('includeSCPI', False)
        super().__init__(adapter, name, **kwargs)

    # Create four channels for the SPD4000X series with proper channel-specific ranges
    channels = Instrument.MultiChannelCreator(SiglentSPD4000XChannel, [1, 2, 3, 4])

    def shutdown(self):
        """
        Safely shutdown all channels of the power supply.

        This method iterates through all channels and calls their individual
        shutdown methods to ensure safe power-down with gradual voltage reduction.
        """
        for channel in self.channels.values():
            channel.shutdown()

    def get_channel_specs(self, channel_id):
        """
        Get the voltage and current specifications for a specific channel.

        Args:
            channel_id (int): Channel number (1, 2, 3, or 4)

        Returns:
            dict: Dictionary containing voltage_range and current_range tuples
        """
        if channel_id in [1, 4]:  # High precision channels
            return {
                'voltage_range': (0, 15.0),
                'current_range': (0, 1.5),
                'type': 'High Precision'
            }
        elif channel_id in [2, 3]:  # High current channels
            return {
                'voltage_range': (0, 12.0),
                'current_range': (0, 10.0),
                'type': 'High Current'
            }
        else:
            raise ValueError(f"Invalid channel ID: {channel_id}. Must be 1, 2, 3, or 4.")


"""
Frequency Sweep Measurement Procedure

This module provides a PyMeasure procedure for performing frequency sweep measurements
using a Toptica laser, Siglent power supply, and Ocean Optics spectrometer.

The procedure:
1. Sets a specific voltage on the power supply
2. Sweeps the laser frequency across a defined range
3. Captures spectrometer data at each frequency point
4. Records the maximum intensity value from the spectrometer
"""

import sys
import numpy as np
import time
import logging
from pymeasure.experiment import Procedure, FloatParameter, IntegerParameter, Parameter
from pymeasure.display.windows import ManagedWindow

# Import seabreeze for spectrometer control
import seabreeze
seabreeze.use('cseabreeze')
try:
    import seabreeze.spectrometers
except ImportError:
    raise ImportError(
        "Seabreeze is required for spectrometer control. Install with: pip install seabreeze"
    )

# Import our custom instrument drivers
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from instrument.toptica_ctl import TopticaCTL
from instrument.siglent_spd import SiglentSPD4000X

try:
    from PyQt5.QtWidgets import QApplication
except ImportError:
    try:
        from PySide6.QtWidgets import QApplication
    except ImportError:
        raise ImportError("PyQt5 or PySide6 is required for the GUI")


class FrequencySweepProcedure(Procedure):
    """
    Procedure for automated frequency sweep measurements.
    
    This procedure coordinates a Toptica laser, Siglent power supply, and 
    Ocean Optics spectrometer to perform frequency-dependent spectroscopy.
    """
    
    def __init__(self):
        super().__init__()
        # Initialize logger
        self.log = logging.getLogger(__name__)
        # Set up basic logging configuration if not already configured
        if not self.log.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.log.addHandler(handler)
            self.log.setLevel(logging.INFO)
    
    # Instrument connection parameters
    laser_ip = Parameter(
        'Laser IP Address',
        default='************'
    )
    
    power_supply_ip = Parameter(
        'Power Supply IP Address', 
        default='************'
    )
    
    # Measurement parameters
    voltage_setpoint = FloatParameter(
        'Voltage Setpoint',
        units='V',
        default=5.0,
        minimum=0.0,
        maximum=15.0
    )
    
    power_supply_channel = IntegerParameter(
        'Power Supply Channel',
        default=1,
        minimum=1,
        maximum=4
    )
    
    start_frequency = FloatParameter(
        'Start Frequency',
        units='THz',
        default=193.0,
        minimum=100.0,
        maximum=400.0
    )
    
    stop_frequency = FloatParameter(
        'Stop Frequency',
        units='THz', 
        default=194.0,
        minimum=100.0,
        maximum=400.0
    )
    
    frequency_steps = IntegerParameter(
        'Frequency Steps',
        default=21,
        minimum=2,
        maximum=1000
    )
    
    integration_time = IntegerParameter(
        'Integration Time',
        units='ms',
        default=100,
        minimum=1,
        maximum=10000
    )
    
    settling_time = FloatParameter(
        'Settling Time',
        units='s',
        default=0.5,
        minimum=0.0,
        maximum=10.0
    )
    
    # Data columns for results
    DATA_COLUMNS = [
        'Frequency (THz)',
        'Wavelength (nm)', 
        'Set Frequency (THz)',
        'Actual Wavelength (nm)',
        'Max Intensity (arb.)',
        'Peak Wavelength (nm)',
        'Voltage (V)',
        'Current (A)'
    ]
    
    def startup(self):
        """
        Initialize instruments and configure measurement setup.
        """
        self.log.info("Starting frequency sweep measurement...")
        
        # Initialize Toptica laser
        try:
            self.log.info(f"Connecting to Toptica laser at {self.laser_ip}")
            self.laser = TopticaCTL(self.laser_ip)
            self.log.info(f"Connected to laser: {self.laser.serial_number}")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to laser: {e}")
        
        # Initialize power supply
        try:
            self.log.info(f"Connecting to power supply at {self.power_supply_ip}")
            self.power_supply = SiglentSPD4000X(f"TCPIP::{self.power_supply_ip}::INSTR")
            self.channel = self.power_supply.channels[self.power_supply_channel]
            self.log.info("Connected to power supply")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to power supply: {e}")
        
        # Initialize spectrometer
        try:
            self.log.info("Connecting to spectrometer...")            
            self.spectrometer = seabreeze.spectrometers.Spectrometer.from_first_available()
            self.spectrometer.integration_time_micros(self.integration_time * 1000)
            self.log.info("Connected to spectrometer")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to spectrometer: {e}")
        
        # Configure power supply
        self.log.info(f"Setting power supply CH{self.power_supply_channel} to {self.voltage_setpoint}V")
        self.channel.current_limit = 1.0  # Set reasonable current limit
        self.channel.voltage = self.voltage_setpoint
        self.channel.output_enabled = True
        
        # Configure laser
        self.log.info("Enabling laser emission")
        self.laser.emission_enabled = True
        
        # Allow settling time
        time.sleep(1.0)
        
        # Check system health
        health = self.laser.system_health
        if health.lower() not in ['ok', 'nominal', 'normal']:
            self.log.warning(f"Laser system health: {health}")
    
    def execute(self):
        """
        Execute the frequency sweep measurement.
        """
        # Generate frequency points
        frequency_points = np.linspace(
            self.start_frequency,
            self.stop_frequency, 
            self.frequency_steps
        )
        
        self.log.info(f"Starting frequency sweep: {self.start_frequency} to {self.stop_frequency} THz")
        self.log.info(f"Number of points: {self.frequency_steps}")
        
        for i, target_frequency in enumerate(frequency_points):
            # Check if measurement should stop
            if self.should_stop():
                self.log.info("Measurement stopped by user")
                break
            
            self.log.info(f"Point {i+1}/{self.frequency_steps}: Setting frequency to {target_frequency:.3f} THz")
            
            # Set laser frequency
            try:
                self.laser.frequency = target_frequency
                
                # Allow settling time
                time.sleep(self.settling_time)
                
                # Read actual laser parameters
                actual_wavelength = self.laser.wavelength_actual
                actual_frequency = self.laser.frequency
                
                # Read power supply measurements
                voltage = self.channel.measure_voltage
                current = self.channel.measure_current
                
                # Acquire spectrum
                wavelengths = self.spectrometer.wavelengths()
                intensities = self.spectrometer.intensities()
                
                # Find peak wavelength and maximum intensity
                peak_index = np.argmax(intensities)
                peak_wavelength = wavelengths[peak_index]
                max_intensity = intensities[peak_index]
                
                # Convert target frequency to wavelength for reference
                c = 299.792458  # Speed of light in THz·nm
                target_wavelength = c / target_frequency
                
                # Prepare data
                data = {
                    'Frequency (THz)': actual_frequency,
                    'Wavelength (nm)': actual_wavelength,
                    'Set Frequency (THz)': target_frequency,
                    'Actual Wavelength (nm)': actual_wavelength,
                    'Max Intensity (arb.)': max_intensity,
                    'Peak Wavelength (nm)': peak_wavelength,
                    'Voltage (V)': voltage,
                    'Current (A)': current
                }
                
                # Emit results
                self.emit('results', data)
                
                # Update progress
                progress = (i + 1) / len(frequency_points) * 100
                self.emit('progress', progress)
                
                self.log.info(f"  Actual: {actual_frequency:.3f} THz ({actual_wavelength:.2f} nm)")
                self.log.info(f"  Max intensity: {max_intensity:.1f} at {peak_wavelength:.2f} nm")
                
            except Exception as e:
                self.log.error(f"Error at frequency {target_frequency:.3f} THz: {e}")
                # Continue with next point
                continue
    
    def shutdown(self):
        """
        Safely shutdown all instruments after measurement completion.
        """
        self.log.info("Shutting down instruments...")
        
        # Shutdown laser
        try:
            if hasattr(self, 'laser'):
                self.log.info("Disabling laser emission")
                self.laser.emission_enabled = False
                self.laser.shutdown()
        except Exception as e:
            self.log.error(f"Error shutting down laser: {e}")
        
        # Shutdown power supply
        try:
            if hasattr(self, 'power_supply'):
                self.log.info("Shutting down power supply")
                self.power_supply.shutdown()
        except Exception as e:
            self.log.error(f"Error shutting down power supply: {e}")
        
        # Close spectrometer
        try:
            if hasattr(self, 'spectrometer'):
                self.log.info("Closing spectrometer")
                self.spectrometer.close()
        except Exception as e:
            self.log.error(f"Error closing spectrometer: {e}")
        
        self.log.info("Shutdown complete")


class FrequencySweepWindow(ManagedWindow):
    """
    Main application window for the frequency sweep measurement system.
    """
    
    def __init__(self):
        super().__init__(
            procedure_class=FrequencySweepProcedure,
            inputs=[
                'laser_ip',
                'power_supply_ip', 
                'voltage_setpoint',
                'power_supply_channel',
                'start_frequency',
                'stop_frequency',
                'frequency_steps',
                'integration_time',
                'settling_time'
            ],
            displays=[
                'laser_ip',
                'power_supply_ip',
                'voltage_setpoint', 
                'power_supply_channel',
                'start_frequency',
                'stop_frequency',
                'frequency_steps',
                'integration_time',
                'settling_time'
            ],
            x_axis='Frequency (THz)',
            y_axis='Max Intensity (arb.)'
        )
        self.setWindowTitle('Frequency Sweep Measurement System')


if __name__ == "__main__":
    # Create QApplication instance
    app = QApplication(sys.argv)
    
    # Create and show main window
    window = FrequencySweepWindow()
    window.show()
    
    # Start application event loop
    sys.exit(app.exec())

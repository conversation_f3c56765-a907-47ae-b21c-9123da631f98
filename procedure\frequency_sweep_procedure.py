"""
Wavelength Sweep Measurement Procedure

本模块提供基于 PyMeasure 的“波长扫描”流程：控制 Toptica 激光器按波长（nm）在给定范围内扫描，
并同步记录光谱仪与电源读数。

流程：
1. 设定电源输出电压
2. 按“波长（nm）”在起止范围内等步长扫描
3. 每个波长点采集光谱数据
4. 记录最大强度与峰值波长，同时保存电源读数

注意（单位一致性）：
- 与 `instrument/toptica_ctl.py` 保持一致：直接设置 `laser.wavelength`（单位：nm）。
- 若需要频率（THz），通过 `laser.frequency` 或 `c / wavelength_nm` 计算得到（c=299.792458 THz·nm）。
"""

import sys
import numpy as np
import time
import logging
from pymeasure.experiment import Procedure, FloatParameter, IntegerParameter, Parameter
from pymeasure.display.windows import ManagedWindow

# Import seabreeze for spectrometer control
import seabreeze
seabreeze.use('cseabreeze')
try:
    import seabreeze.spectrometers
except ImportError:
    raise ImportError(
        "Seabreeze is required for spectrometer control. Install with: pip install seabreeze"
    )

# Import our custom instrument drivers
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from instrument.toptica_ctl import TopticaCTL
from instrument.siglent_spd import SiglentSPD4000X

try:
    from PyQt5.QtWidgets import QApplication
except ImportError:
    try:
        from PySide6.QtWidgets import QApplication
    except ImportError:
        raise ImportError("PyQt5 or PySide6 is required for the GUI")


class FrequencySweepProcedure(Procedure):
    """
    Procedure for automated frequency sweep measurements.
    
    This procedure coordinates a Toptica laser, Siglent power supply, and 
    Ocean Optics spectrometer to perform frequency-dependent spectroscopy.
    """
    
    def __init__(self):
        super().__init__()
        # Initialize logger
        self.log = logging.getLogger(__name__)
        # Set up basic logging configuration if not already configured
        if not self.log.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.log.addHandler(handler)
            self.log.setLevel(logging.INFO)
    
    # Instrument connection parameters
    laser_ip = Parameter(
        'Laser IP Address',
        default='************'
    )
    
    power_supply_ip = Parameter(
        'Power Supply IP Address', 
        default='************'
    )
    
    # Measurement parameters
    voltage_setpoint = FloatParameter(
        'Voltage Setpoint',
        units='V',
        default=5.0,
        minimum=0.0,
        maximum=15.0
    )
    
    power_supply_channel = IntegerParameter(
        'Power Supply Channel',
        default=1,
        minimum=1,
        maximum=4
    )
    
    start_wavelength = FloatParameter(
        'Start Wavelength',
        units='nm',
        default=1550.0,
        minimum=1510.0,
        maximum=1620.0
    )

    stop_wavelength = FloatParameter(
        'Stop Wavelength',
        units='nm',
        default=1549.0,
        minimum=1510.0,
        maximum=1620.0
    )

    wavelength_steps = IntegerParameter(
        'Wavelength Steps',
        default=21,
        minimum=2,
        maximum=1000
    )
    
    integration_time = IntegerParameter(
        'Integration Time',
        units='ms',
        default=100,
        minimum=1,
        maximum=10000
    )
    
    settling_time = FloatParameter(
        'Settling Time',
        units='s',
        default=0.5,
        minimum=0.0,
        maximum=10.0
    )
    
    # Data columns for results
    DATA_COLUMNS = [
        'Wavelength (nm)',
        'Frequency (THz)',
        'Set Wavelength (nm)',
        'Actual Wavelength (nm)',
        'Max Intensity (arb.)',
        'Peak Wavelength (nm)',
        'Voltage (V)',
        'Current (A)'
    ]
    
    def startup(self):
        """
        Initialize instruments and configure measurement setup.
        """
        self.log.info("Starting wavelength sweep measurement...")
        
        # Initialize Toptica laser
        try:
            self.log.info(f"Connecting to Toptica laser at {self.laser_ip}")
            self.laser = TopticaCTL(self.laser_ip)
            self.log.info(f"Connected to laser: {self.laser.serial_number}")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to laser: {e}")
        
        # Initialize power supply
        try:
            self.log.info(f"Connecting to power supply at {self.power_supply_ip}")
            self.power_supply = SiglentSPD4000X(f"TCPIP::{self.power_supply_ip}::INSTR")
            self.channel = self.power_supply.channels[self.power_supply_channel]
            self.log.info("Connected to power supply")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to power supply: {e}")
        
        # Initialize spectrometer
        try:
            self.log.info("Connecting to spectrometer...")            
            self.spectrometer = seabreeze.spectrometers.Spectrometer.from_first_available()
            self.spectrometer.integration_time_micros(self.integration_time * 1000)
            self.log.info("Connected to spectrometer")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to spectrometer: {e}")
        
        # Configure power supply
        self.log.info(f"Setting power supply CH{self.power_supply_channel} to {self.voltage_setpoint}V")
        self.channel.current_limit = 1.0  # Set reasonable current limit
        self.channel.voltage = self.voltage_setpoint
        self.channel.output_enabled = True
        
        # Configure laser
        self.log.info("Enabling laser emission")
        self.laser.emission_enabled = True
        
        # Allow settling time
        time.sleep(1.0)
        
        # Check system health
        health = self.laser.system_health
        if health.lower() not in ['ok', 'nominal', 'normal']:
            self.log.warning(f"Laser system health: {health}")
    
    def execute(self):
        """
        Execute the wavelength sweep measurement.
        """
        # Generate wavelength points (nm)
        wavelength_points = np.linspace(
            self.start_wavelength,
            self.stop_wavelength,
            self.wavelength_steps
        )

        self.log.info(
            f"Starting wavelength sweep: {self.start_wavelength} to {self.stop_wavelength} nm"
        )
        self.log.info(f"Number of points: {self.wavelength_steps}")

        c = 299.792458  # THz·nm

        for i, target_wavelength in enumerate(wavelength_points):
            # Check if measurement should stop
            if self.should_stop():
                self.log.info("Measurement stopped by user")
                break
            
            self.log.info(
                f"Point {i+1}/{self.wavelength_steps}: Setting wavelength to {target_wavelength:.3f} nm"
            )
            
            # Set laser wavelength (nm)
            try:
                self.laser.wavelength = target_wavelength
                
                # Allow settling time
                time.sleep(self.settling_time)
                
                # Read actual laser parameters
                actual_wavelength = self.laser.wavelength_actual
                actual_frequency = self.laser.frequency  # derived from actual wavelength
                
                # Read power supply measurements
                voltage = self.channel.measure_voltage
                current = self.channel.measure_current
                
                # Acquire spectrum
                wavelengths = self.spectrometer.wavelengths()
                intensities = self.spectrometer.intensities()
                
                # Find peak wavelength and maximum intensity
                peak_index = np.argmax(intensities)
                peak_wavelength = wavelengths[peak_index]
                max_intensity = intensities[peak_index]
                
                # Compute target frequency from target wavelength (for record)
                target_frequency = c / target_wavelength
                
                # Prepare data
                data = {
                    'Wavelength (nm)': actual_wavelength,
                    'Frequency (THz)': actual_frequency,
                    'Set Wavelength (nm)': target_wavelength,
                    'Actual Wavelength (nm)': actual_wavelength,
                    'Max Intensity (arb.)': max_intensity,
                    'Peak Wavelength (nm)': peak_wavelength,
                    'Voltage (V)': voltage,
                    'Current (A)': current
                }
                
                # Emit results
                self.emit('results', data)
                
                # Update progress
                progress = (i + 1) / len(wavelength_points) * 100
                self.emit('progress', progress)
                
                self.log.info(f"  Actual: {actual_wavelength:.2f} nm ({actual_frequency:.3f} THz)")
                self.log.info(f"  Max intensity: {max_intensity:.1f} at {peak_wavelength:.2f} nm")
                
            except Exception as e:
                self.log.error(f"Error at wavelength {target_wavelength:.3f} nm: {e}")
                # Continue with next point
                continue
    
    def shutdown(self):
        """
        Safely shutdown all instruments after measurement completion.
        """
        self.log.info("Shutting down instruments...")
        
        # Shutdown laser
        try:
            if hasattr(self, 'laser'):
                self.log.info("Disabling laser emission")
                self.laser.emission_enabled = False
                self.laser.shutdown()
        except Exception as e:
            self.log.error(f"Error shutting down laser: {e}")
        
        # Shutdown power supply
        try:
            if hasattr(self, 'power_supply'):
                self.log.info("Shutting down power supply")
                self.power_supply.shutdown()
        except Exception as e:
            self.log.error(f"Error shutting down power supply: {e}")
        
        # Close spectrometer
        try:
            if hasattr(self, 'spectrometer'):
                self.log.info("Closing spectrometer")
                self.spectrometer.close()
        except Exception as e:
            self.log.error(f"Error closing spectrometer: {e}")
        
        self.log.info("Shutdown complete")


class FrequencySweepWindow(ManagedWindow):
    """
    Main application window for the frequency sweep measurement system.
    """
    
    def __init__(self):
        super().__init__(
            procedure_class=FrequencySweepProcedure,
            inputs=[
                'laser_ip',
                'power_supply_ip', 
                'voltage_setpoint',
                'power_supply_channel',
                'start_wavelength',
                'stop_wavelength',
                'wavelength_steps',
                'integration_time',
                'settling_time'
            ],
            displays=[
                'laser_ip',
                'power_supply_ip',
                'voltage_setpoint', 
                'power_supply_channel',
                'start_wavelength',
                'stop_wavelength',
                'wavelength_steps',
                'integration_time',
                'settling_time'
            ],
            x_axis='Wavelength (nm)',
            y_axis='Max Intensity (arb.)'
        )
        self.setWindowTitle('Frequency Sweep Measurement System')


if __name__ == "__main__":
    # Create QApplication instance
    app = QApplication(sys.argv)
    
    # Create and show main window
    window = FrequencySweepWindow()
    window.show()
    
    # Start application event loop
    sys.exit(app.exec())

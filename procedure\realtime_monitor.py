"""
Realtime Monitor Window

功能
- 显示仪器状态（激光器/示波器/电源）
- 实时显示光谱仪数据（波长-强度）
- 实时显示示波器数据（时间-电压，支持选择通道 C1..C4）
- 实时显示电源数据（电压/电流）
- 用户可实时修改关键输入（光谱积分时间、示波器时基、电源电压/限流/开关等），结果即时生效
- 支持保存数据：
  - 示波器屏幕截图（PNG）
  - 多通道同时保存屏幕等效波形（CSV；每通道一文件）
  - 文件名可由用户指定，默认保存在 results/ 目录

使用说明（无仪器也可启动界面）：
1) 安装依赖：
   - 必需：pip install PyQt5
   - 建议：pip install pyqtgraph seabreeze
2) 运行：
   - python procedure/realtime_monitor.py
3) 连接：
   - 界面仅提供“连接/断开”按钮，IP 地址在文件顶部常量中配置（见 DEFAULT_* 常量）
4) 操作：
   - 修改“Spectrometer Integration (ms)”“Scope Timebase (s/div)”“PSU 输出参数”等，立即生效
    - “Scope Channels”选择用于显示/保存的示波器通道（C1..C4）
5) 保存：
   - “保存示波器截图”：保存当前屏幕 PNG 到你选择的路径
   - “保存所选通道波形”：保存选定通道的屏幕等效波形 CSV（列：time(s), voltage(V)）

编程方法（如何扩展）：
- 新增一个“输入控件”：
  1) 在“控制”分组中添加相应的控件（如 QDoubleSpinBox），设置范围/步进/单位后，
  2) 将 valueChanged/toggled 信号连接到一个回调函数，
  3) 在回调中调用相应驱动属性/方法（例如 `self._scope.timebase_scale = 值`）。
- 新增一个“输出显示”：
  1) 在“实时数据”区域添加标签/曲线，
  2) 在 `_poll_loop` 中从仪器读取数据并更新显示对象（如 `setText` 或 `setData`）。
- 保存更多数据：
  - 示波器：调用 `self._scope.screenshot_png(path)` 保存屏幕图，或对每个通道调用 `fetch_waveform_screen(ch)` 保存 CSV。
  - 电源：定期将 `measure_voltage/measure_current` 采样写入 CSV（可在 `_poll_loop` 中追加）。
  - 光谱：将 `wavelengths()/intensities()` 写入 CSV（可在点击按钮时采样一次）。
"""

from __future__ import annotations

import sys
import time
import threading
from typing import Optional

from PyQt5.QtWidgets import (
    QApplication, QWidget, QGridLayout, QHBoxLayout, QVBoxLayout, QLabel,
    QLineEdit, QPushButton, QGroupBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QFileDialog, QMessageBox, QCheckBox as QtCheckBox, QTabWidget, QSplitter
)
from PyQt5.QtWidgets import QSizePolicy
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPalette, QColor, QFont

try:
    import pyqtgraph as pg
    _HAS_PG = True
except Exception:
    _HAS_PG = False

# 仪器驱动
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from instrument.toptica_ctl import TopticaCTL
from instrument.siglent_spd import SiglentSPD4000X
from instrument.sds6108_h10_pro import SDS6108H10Pro


# ----------------------- 配置：在此处设置默认 IP 地址 -----------------------
DEFAULT_LASER_IP = "************"
DEFAULT_PSU_IP = "************"
DEFAULT_SCOPE_IP = "************"

class RealtimeMonitorWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Realtime Instrument Monitor")

        # 状态
        self._laser: Optional[TopticaCTL] = None
        self._psu: Optional[SiglentSPD4000X] = None
        self._psu_ch = None
        self._scope: Optional[SDS6108H10Pro] = None
        self._spectrometer = None
        self._running = False

        # 初始化界面与主题
        self._build_ui()
        self._apply_dark_theme()

        # 定时器轮询（在 GUI 线程中执行，避免跨线程启动 Qt 定时器导致的错误）
        self._timer = QTimer(self)
        self._timer.setInterval(200)  # ms
        self._timer.timeout.connect(self._on_timer_tick)
        self._timer.start()

    # ----------------------- UI -----------------------
    def _build_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)

        # 连接区域
        conn_group = QGroupBox("连接")
        conn_layout = QGridLayout(conn_group)

        self.btn_connect = QPushButton("连接")
        self.btn_disconnect = QPushButton("断开")
        self.btn_connect.clicked.connect(self._on_connect)
        self.btn_disconnect.clicked.connect(self._on_disconnect)

        row = 0
        conn_layout.addWidget(self.btn_connect, row, 0)
        conn_layout.addWidget(self.btn_disconnect, row, 1)

        # 状态显示
        status_group = QGroupBox("仪器状态")
        status_layout = QGridLayout(status_group)
        self.lbl_laser = QLabel("Laser: 未连接")
        self.lbl_scope = QLabel("Scope: 未连接")
        self.lbl_psu = QLabel("PSU: 未连接")
        self.lbl_spec = QLabel("Spectrometer: 未连接")
        status_layout.addWidget(self.lbl_laser, 0, 0)
        status_layout.addWidget(self.lbl_scope, 1, 0)
        status_layout.addWidget(self.lbl_psu, 2, 0)
        status_layout.addWidget(self.lbl_spec, 3, 0)

        # 控制区：光谱仪/示波器/电源
        ctrl_group = QGroupBox("控制")
        ctrl_layout = QGridLayout(ctrl_group)

        # 光谱仪
        self.spec_int_ms = QSpinBox()
        self.spec_int_ms.setRange(1, 10000)
        self.spec_int_ms.setValue(100)
        self.spec_int_ms.setSuffix(" ms")
        self.spec_int_ms.valueChanged.connect(self._on_spec_integration_changed)

        # 示波器
        self.scope_timebase = QDoubleSpinBox()
        self.scope_timebase.setDecimals(9)
        self.scope_timebase.setRange(1e-9, 1000.0)
        self.scope_timebase.setSingleStep(1e-6)
        self.scope_timebase.setValue(1e-3)  # s/div
        self.scope_timebase.setSuffix(" s/div")
        self.scope_timebase.valueChanged.connect(self._on_scope_timebase_changed)
        # 选择用于显示/保存的示波器通道（C1..C4，可多选）- 网格布局
        self.scope_channel_checks = []
        ch_container = QWidget(); ch_v = QVBoxLayout(ch_container); ch_v.setContentsMargins(0,0,0,0); ch_v.setSpacing(4)
        ch_grid = QGridLayout(); ch_grid.setContentsMargins(0,0,0,0); ch_grid.setHorizontalSpacing(12); ch_grid.setVerticalSpacing(4)
        for idx, ch in enumerate(range(1, 5)):
            cb = QtCheckBox(f"C{ch}")
            cb.setChecked(ch == 1)
            self.scope_channel_checks.append(cb)
            r, c = divmod(idx, 2)
            ch_grid.addWidget(cb, r, c)
        ch_v.addLayout(ch_grid)
        # 不再提供全选/反选快捷按钮，避免占位

        # 电源
        self.psu_channel = QSpinBox()
        self.psu_channel.setRange(1, 4)
        self.psu_channel.setValue(1)
        self.psu_v = QDoubleSpinBox()
        self.psu_v.setDecimals(3)
        self.psu_v.setRange(0.0, 20.0)
        self.psu_v.setSingleStep(0.1)
        self.psu_v.setValue(5.0)
        self.psu_v.setSuffix(" V")
        self.psu_i = QDoubleSpinBox()
        self.psu_i.setDecimals(3)
        self.psu_i.setRange(0.0, 10.0)
        self.psu_i.setSingleStep(0.1)
        self.psu_i.setValue(1.0)
        self.psu_i.setSuffix(" A")
        self.psu_out = QCheckBox("输出开")
        self.psu_channel.valueChanged.connect(self._on_psu_channel_changed)
        self.psu_v.valueChanged.connect(self._on_psu_voltage_changed)
        self.psu_i.valueChanged.connect(self._on_psu_current_changed)
        self.psu_out.toggled.connect(self._on_psu_output_toggled)

        # 控制布局
        r = 0
        ctrl_layout.addWidget(QLabel("Spectrometer Integration:"), r, 0)
        ctrl_layout.addWidget(self.spec_int_ms, r, 1)
        r += 1
        ctrl_layout.addWidget(QLabel("Scope Timebase:"), r, 0)
        ctrl_layout.addWidget(self.scope_timebase, r, 1)
        r += 1
        ctrl_layout.addWidget(QLabel("Scope Channels:"), r, 0)
        ctrl_layout.addWidget(ch_container, r, 1)
        r += 1
        ctrl_layout.addWidget(QLabel("PSU Channel:"), r, 0)
        ctrl_layout.addWidget(self.psu_channel, r, 1)
        r += 1
        ctrl_layout.addWidget(QLabel("PSU Voltage:"), r, 0)
        ctrl_layout.addWidget(self.psu_v, r, 1)
        r += 1
        ctrl_layout.addWidget(QLabel("PSU Current Limit:"), r, 0)
        ctrl_layout.addWidget(self.psu_i, r, 1)
        r += 1
        ctrl_layout.addWidget(self.psu_out, r, 0, 1, 2)

        # 实时显示区（左侧，用垂直分割器可自由调整两张图比例）
        left_splitter = QSplitter(Qt.Vertical)
        if _HAS_PG:
            # 提升显示质量
            try:
                pg.setConfigOptions(antialias=True, background=(30, 30, 30), foreground='w')
            except Exception:
                pass
            self.pg_scope = pg.PlotWidget(title="Oscilloscope (Voltage vs Time)")
            self.pg_scope.setLabel('bottom', 'Time', units='s')
            self.pg_scope.setLabel('left', 'Voltage', units='V')
            self.curve_scope = self.pg_scope.plot(pen=pg.mkPen('c', width=2))
            try:
                self.pg_scope.addLegend(offset=(10, 10))
            except Exception:
                pass
            try:
                self.pg_scope.getPlotItem().showGrid(x=True, y=True, alpha=0.2)
                f = QFont(); f.setPointSize(10)
                self.pg_scope.getPlotItem().getAxis('left').setTickFont(f)
                self.pg_scope.getPlotItem().getAxis('bottom').setTickFont(f)
            except Exception:
                pass

            self.pg_spec = pg.PlotWidget(title="Spectrometer (Intensity vs Wavelength)")
            self.pg_spec.setLabel('bottom', 'Wavelength', units='nm')
            self.pg_spec.setLabel('left', 'Intensity', units='arb.')
            self.curve_spec = self.pg_spec.plot(pen=pg.mkPen('y', width=2))
            try:
                self.pg_spec.getPlotItem().showGrid(x=True, y=True, alpha=0.2)
                f2 = QFont(); f2.setPointSize(10)
                self.pg_spec.getPlotItem().getAxis('left').setTickFont(f2)
                self.pg_spec.getPlotItem().getAxis('bottom').setTickFont(f2)
            except Exception:
                pass

            left_splitter.addWidget(self.pg_scope)
            left_splitter.addWidget(self.pg_spec)
            left_splitter.setStretchFactor(0, 3)
            left_splitter.setStretchFactor(1, 2)
        else:
            left_splitter.addWidget(QLabel("pyqtgraph 未安装，无法显示曲线。请: pip install pyqtgraph"))

        # 电源读数
        psu_group = QGroupBox("电源读数")
        psu_layout = QHBoxLayout(psu_group)
        self.lbl_psu_v = QLabel("V: - V")
        self.lbl_psu_i = QLabel("I: - A")
        psu_layout.addWidget(self.lbl_psu_v)
        psu_layout.addWidget(self.lbl_psu_i)

        # 保存区域（独立分组，排版更紧凑）
        save_group = QGroupBox("保存")
        from PyQt5.QtWidgets import QFormLayout
        save_form = QFormLayout(save_group)
        self.save_basename_edit = QLineEdit("results/scope")
        self.btn_save_scope_png = QPushButton("保存示波器截图")
        self.btn_save_wave_csv = QPushButton("保存所选通道波形")
        self.btn_save_scope_png.clicked.connect(self._on_save_scope_png)
        self.btn_save_wave_csv.clicked.connect(self._on_save_wave_csv)
        btns = QWidget(); btns_layout = QHBoxLayout(btns); btns_layout.setContentsMargins(0,0,0,0); btns_layout.setSpacing(8)
        btns_layout.addWidget(self.btn_save_scope_png)
        btns_layout.addWidget(self.btn_save_wave_csv)
        save_form.addRow("保存基名:", self.save_basename_edit)
        save_form.addRow("操作:", btns)

        # 右侧：使用 Tab，布局更紧凑
        right_tabs = QTabWidget()
        right_tabs.setMinimumWidth(360)
        right_tabs.setMaximumWidth(520)

        # 连接页（同时显示当前 IP 常量）
        conn_page = QWidget(); conn_page_layout = QVBoxLayout(conn_page)
        conn_page_layout.addWidget(QLabel(f"Laser IP: {DEFAULT_LASER_IP}"))
        conn_page_layout.addWidget(QLabel(f"PSU IP: {DEFAULT_PSU_IP}"))
        conn_page_layout.addWidget(QLabel(f"Scope IP: {DEFAULT_SCOPE_IP}"))
        conn_row = QHBoxLayout(); conn_row.addWidget(self.btn_connect); conn_row.addWidget(self.btn_disconnect); conn_row.addStretch(1)
        conn_page_layout.addLayout(conn_row)
        conn_page_layout.addStretch(1)
        right_tabs.addTab(conn_page, "连接")

        # 控制页
        ctrl_page = QWidget(); ctrl_page_layout = QVBoxLayout(ctrl_page)
        ctrl_page_layout.addWidget(ctrl_group)
        ctrl_page_layout.addStretch(1)
        right_tabs.addTab(ctrl_page, "控制")

        # 状态/保存页
        status_page = QWidget(); status_page_layout = QVBoxLayout(status_page)
        status_page_layout.addWidget(status_group)
        status_page_layout.addWidget(psu_group)
        status_page_layout.addWidget(save_group)
        status_page_layout.addStretch(1)
        right_tabs.addTab(status_page, "状态/保存")

        # 主布局：左（图）大，右（控件）小
        main_layout.addWidget(left_splitter, stretch=5)
        main_layout.addWidget(right_tabs, stretch=2)

    def _apply_dark_theme(self):
        """应用简洁暗色主题，使界面更美观一致。"""
        app = QApplication.instance()
        if app is None:
            return
        try:
            app.setStyle("Fusion")
            palette = QPalette()
            bg = QColor(30, 30, 30)
            mid = QColor(45, 45, 45)
            text = QColor(220, 220, 220)
            palette.setColor(QPalette.Window, bg)
            palette.setColor(QPalette.WindowText, text)
            palette.setColor(QPalette.Base, QColor(25, 25, 25))
            palette.setColor(QPalette.AlternateBase, mid)
            palette.setColor(QPalette.ToolTipBase, text)
            palette.setColor(QPalette.ToolTipText, text)
            palette.setColor(QPalette.Text, text)
            palette.setColor(QPalette.Button, mid)
            palette.setColor(QPalette.ButtonText, text)
            palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
            palette.setColor(QPalette.Highlight, QColor(64, 128, 255))
            palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
            app.setPalette(palette)
            # 细节样式
            app.setStyleSheet("""
                QGroupBox { border: 1px solid #444; border-radius: 6px; margin-top: 8px; }
                QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 4px; }
                QLabel { color: #ddd; }
                QTabWidget::pane { border: 1px solid #444; }
                QTabBar::tab { background: #2d2d2d; padding: 6px 10px; }
                QTabBar::tab:selected { background: #3a3a3a; }
                QPushButton { padding: 6px 10px; }
                QLineEdit { padding: 4px; }
            """)
        except Exception:
            pass

    # ----------------------- 连接/断开 -----------------------
    def _on_connect(self):
        # 激光器
        try:
            self._laser = TopticaCTL(DEFAULT_LASER_IP)
            self.lbl_laser.setText(f"Laser: OK (SN {self._laser.serial_number})")
        except Exception as e:
            self._laser = None
            self.lbl_laser.setText(f"Laser: 连接失败 - {e}")

        # 电源
        try:
            self._psu = SiglentSPD4000X(f"TCPIP::{DEFAULT_PSU_IP}::INSTR")
            ch = int(self.psu_channel.value())
            self._psu_ch = self._psu.channels[ch]
            # 初始设置
            self._psu_ch.current_limit = float(self.psu_i.value())
            self._psu_ch.voltage = float(self.psu_v.value())
            self._psu_ch.output_enabled = self.psu_out.isChecked()
            self.lbl_psu.setText("PSU: OK")
        except Exception as e:
            self._psu = None
            self._psu_ch = None
            self.lbl_psu.setText(f"PSU: 连接失败 - {e}")

        # 示波器
        try:
            # 支持直接传 IP，驱动内部会自动补全 VISA 字符串
            self._scope = SDS6108H10Pro(DEFAULT_SCOPE_IP)
            self._scope.timebase_scale = float(self.scope_timebase.value())
            # 尝试打开 C1
            try:
                ch1 = self._scope.channels[1]
                ch1.display = True
            except Exception:
                pass
            self.lbl_scope.setText(f"Scope: OK ({self._scope.idn})")
        except Exception as e:
            self._scope = None
            self.lbl_scope.setText(f"Scope: 连接失败 - {e}")

        # 光谱仪
        try:
            import seabreeze
            seabreeze.use('cseabreeze')
            import seabreeze.spectrometers as sb
            self._spectrometer = sb.Spectrometer.from_first_available()
            self._spectrometer.integration_time_micros(self.spec_int_ms.value() * 1000)
            self.lbl_spec.setText("Spectrometer: OK")
        except Exception as e:
            self._spectrometer = None
            self.lbl_spec.setText(f"Spectrometer: 连接失败 - {e}")

    def _on_disconnect(self):
        try:
            if self._laser is not None:
                self._laser.shutdown()
        except Exception:
            pass
        try:
            if self._psu is not None:
                self._psu.shutdown()
        except Exception:
            pass
        try:
            if self._spectrometer is not None:
                self._spectrometer.close()
        except Exception:
            pass
        self._laser = None
        self._psu = None
        self._psu_ch = None
        self._scope = None
        self._spectrometer = None
        self.lbl_laser.setText("Laser: 未连接")
        self.lbl_scope.setText("Scope: 未连接")
        self.lbl_psu.setText("PSU: 未连接")
        self.lbl_spec.setText("Spectrometer: 未连接")

    # ----------------------- 控制回调 -----------------------
    def _on_spec_integration_changed(self, ms: int):
        if self._spectrometer is not None:
            try:
                self._spectrometer.integration_time_micros(ms * 1000)
            except Exception:
                pass

    def _on_scope_timebase_changed(self, s_per_div: float):
        if self._scope is not None:
            try:
                self._scope.timebase_scale = float(s_per_div)
            except Exception:
                pass

    def _on_psu_channel_changed(self, ch: int):
        if self._psu is not None:
            try:
                self._psu_ch = self._psu.channels[int(ch)]
            except Exception:
                self._psu_ch = None

    def _on_psu_voltage_changed(self, v: float):
        if self._psu_ch is not None:
            try:
                self._psu_ch.voltage = float(v)
            except Exception:
                pass

    def _on_psu_current_changed(self, a: float):
        if self._psu_ch is not None:
            try:
                self._psu_ch.current_limit = float(a)
            except Exception:
                pass

    def _on_psu_output_toggled(self, on: bool):
        if self._psu_ch is not None:
            try:
                self._psu_ch.output_enabled = bool(on)
            except Exception:
                pass

    # ----------------------- 轮询线程 -----------------------
    def _on_timer_tick(self):
        """在 GUI 线程中定时刷新，避免跨线程更新 Qt 对象。"""
        # Spectrometer
        if self._spectrometer is not None and _HAS_PG:
            try:
                wl = self._spectrometer.wavelengths()
                it = self._spectrometer.intensities()
                self.curve_spec.setData(wl, it)
            except Exception:
                pass

        # Scope waveform（屏幕数据，多通道）
        if self._scope is not None and _HAS_PG:
            try:
                selected = [i+1 for i, cb in enumerate(self.scope_channel_checks) if cb.isChecked()]
                if not hasattr(self, 'scope_curves'):
                    self.scope_curves = {}
                    # 为每个通道创建曲线
                    pens = ['#1abc9c', '#3498db', '#e67e22', '#e74c3c']
                    for ch in range(1, 5):
                        pen = pens[(ch-1) % len(pens)]
                        self.scope_curves[ch] = self.pg_scope.plot(pen=pen, name=f"C{ch}")
                # 更新被选中通道的数据，未选中通道清空
                for ch in range(1, 5):
                    curve = self.scope_curves[ch]
                    if ch in selected:
                        try:
                            t, v = self._scope.fetch_waveform_screen(ch)
                            curve.setData(t, v)
                        except Exception:
                            pass
                    else:
                        curve.setData([], [])
            except Exception:
                pass

        # PSU readings
        if self._psu_ch is not None:
            try:
                v_meas = self._psu_ch.measure_voltage
                i_meas = self._psu_ch.measure_current
                self.lbl_psu_v.setText(f"V: {v_meas:.3f} V")
                self.lbl_psu_i.setText(f"I: {i_meas:.3f} A")
            except Exception:
                pass

    # ----------------------- 关闭事件 -----------------------
    def closeEvent(self, event):
        try:
            if hasattr(self, "_timer"):
                self._timer.stop()
        except Exception:
            pass
        self._on_disconnect()
        super().closeEvent(event)

    # ----------------------- 保存动作 -----------------------
    def _on_save_scope_png(self):
        if self._scope is None:
            QMessageBox.warning(self, "提示", "示波器未连接")
            return
        # 基名 -> results/xxx_screen.png
        base = self.save_basename_edit.text().strip() or "results/scope"
        os.makedirs(os.path.dirname(base), exist_ok=True)
        if base.lower().endswith('.png'):
            path = base
        else:
            path = base + "_screen.png"
        try:
            self._scope.screenshot_png(path)
            QMessageBox.information(self, "完成", f"已保存：{path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败：{e}")

    def _on_save_wave_csv(self):
        if self._scope is None:
            QMessageBox.warning(self, "提示", "示波器未连接")
            return
        base = self.save_basename_edit.text().strip() or "results/scope"
        os.makedirs(os.path.dirname(base), exist_ok=True)
        selected = [i+1 for i, cb in enumerate(self.scope_channel_checks) if cb.isChecked()]
        if not selected:
            QMessageBox.information(self, "提示", "请至少选择一个通道")
            return
        try:
            import csv
            saved = []
            for ch in selected:
                t, v = self._scope.fetch_waveform_screen(ch)
                path = f"{base}_C{ch}.csv"
                with open(path, "w", newline="") as f:
                    writer = csv.writer(f)
                    writer.writerow(["time_s", "voltage_v"])
                    for ti, vi in zip(t, v):
                        writer.writerow([f"{ti:.12g}", f"{vi:.12g}"])
                saved.append(path)
            QMessageBox.information(self, "完成", "已保存:\n" + "\n".join(saved))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败：{e}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    w = RealtimeMonitorWindow()
    w.resize(1200, 700)
    w.show()
    sys.exit(app.exec_())



"""
Toptica CTL Laser Driver for PyMeasure

This module provides a PyMeasure driver for Toptica CTL series lasers controlled
by DLC pro controllers. It integrates the official Toptica Python Laser SDK
with PyMeasure's instrument framework.

The driver supports:
- Wavelength and frequency control
- Current and temperature monitoring/control
- Laser emission control
- Frequency scanning capabilities
- Proper error handling and validation
"""

import time
from pymeasure.instruments import Instrument

try:
    from toptica.lasersdk.client import Client, NetworkConnection, UserLevel, DecopError
except ImportError:
    raise ImportError(
        "Toptica Python Laser SDK is required. Install with: pip install toptica-lasersdk"
    )


class TopticaCTL(Instrument):
    """
    Toptica CTL Series Laser Driver
    
    This class provides a PyMeasure driver for Toptica CTL series lasers with DLC pro
    controllers. It bridges the Toptica SDK with PyMeasure's instrument framework.
    
    The driver uses the low-level Toptica SDK Client API and overrides PyMeasure's
    read/write methods to provide seamless integration.
    
    Example usage:
        # Connect to laser
        laser = TopticaCTL("*************", user_level=UserLevel.NORMAL)
        
        # Control laser
        laser.emission_enabled = True
        laser.wavelength = 1550.0  # nm
        laser.current_setpoint = 100.0  # mA
        
        # Read measurements
        actual_wavelength = laser.wavelength_actual
        actual_current = laser.current_actual
        emission_status = laser.emission_status  # Read-only emission status
        
        # Frequency scanning
        laser.setup_sweep(start_freq=193.0, stop_freq=194.0, scan_speed=1.0)
        laser.start_sweep()
        
        # Shutdown safely
        laser.shutdown()
    """
    
    def __init__(self, adapter, name="Toptica CTL Laser", user_level=None, password="", **kwargs):
        """
        Initialize the Toptica CTL laser.

        Args:
            adapter (str): IP address of the DLC pro controller (e.g., "*************")
            name (str): Name of the instrument
            user_level (UserLevel, optional): User level for the session (READONLY, NORMAL, MAINTENANCE, SERVICE)
            password (str): Password for elevated user levels (default: "")
            **kwargs: Additional keyword arguments passed to parent Instrument class
        """
        # Initialize Toptica SDK client first
        try:
            self.client = Client(NetworkConnection(adapter))
            self.client.open()

            # Set user level if specified
            if user_level is not None:
                self.client.change_ul(user_level, password)

        except Exception as e:
            raise ConnectionError(f"Failed to connect to Toptica laser at {adapter}: {e}")

        # Call parent __init__ with a dummy adapter since we handle communication ourselves
        super().__init__(adapter=None, name=name, **kwargs)
    
    def read(self, command):
        """
        Read a parameter value from the laser using Toptica SDK.
        
        Args:
            command (str): DeCoP parameter name to read
            
        Returns:
            str: String representation of the parameter value
        """
        try:
            value = self.client.get(command)
            return str(value)
        except DecopError as e:
            raise ConnectionError(f"Failed to read parameter '{command}': {e}")
    
    def write(self, command):
        """
        Write a parameter value to the laser using Toptica SDK.
        
        This method parses PyMeasure command strings and converts them to
        appropriate Toptica SDK calls.
        
        Args:
            command (str): Command string in format "parameter_name value" or "parameter_name"
        """
        try:
            parts = command.split(' ', 1)
            param = parts[0]
            
            if len(parts) == 1:
                # Command without value - execute as command
                self.client.exec(param)
                return
            
            value_str = parts[1]
            
            # Convert string value to appropriate type
            if value_str.lower() in ('true', 'on', '1'):
                value = True
            elif value_str.lower() in ('false', 'off', '0'):
                value = False
            elif value_str.startswith('"') and value_str.endswith('"'):
                value = value_str[1:-1]  # Remove quotes
            else:
                try:
                    # Try to convert to number
                    if '.' in value_str or 'e' in value_str.lower():
                        value = float(value_str)
                    else:
                        value = int(value_str)
                except ValueError:
                    value = value_str  # Keep as string
            
            self.client.set(param, value)
            
        except DecopError as e:
            raise ConnectionError(f"Failed to write command '{command}': {e}")
        except Exception as e:
            raise ValueError(f"Invalid command format '{command}': {e}")
    
    # Measurement properties (read-only) - using direct property definitions
    @property
    def wavelength_actual(self):
        """Actual output wavelength in nanometers."""
        return float(self.read("laser1:ctl:wavelength-act"))

    @property
    def current_actual(self):
        """Actual laser diode current in milliamperes."""
        return float(self.read("laser1:dl:cc:current-act"))

    @property
    def temperature_actual(self):
        """Actual laser diode temperature in degrees Celsius."""
        return float(self.read("laser1:dl:tc:temp-act"))

    @property
    def power_actual(self):
        """Actual output power in milliwatts."""
        return float(self.read("laser1:dl:pc:power-act"))

    @property
    def serial_number(self):
        """Device serial number."""
        return str(self.read("serial-number"))

    @property
    def system_health(self):
        """System health status text."""
        return str(self.read("system-health-txt"))
    
    # Control properties (read/write) - using direct property definitions
    @property
    def emission_enabled(self):
        """Control laser emission state (True for on, False for off)."""
        # Read the laser diode current enable state, which controls emission
        value = str(self.read("laser1:dl:cc:enabled")).lower()
        return value in ('true', '1', 'on')

    @emission_enabled.setter
    def emission_enabled(self, value):
        # Control emission by enabling/disabling the laser diode current
        self.write(f"laser1:dl:cc:enabled {'true' if value else 'false'}")

    @property
    def emission_status(self):
        """Read-only emission status of the laser (True if emitting, False if not)."""
        value = str(self.read("laser1:emission")).lower()
        return value in ('true', '1', 'on')

    @property
    def wavelength(self):
        """Control the laser wavelength in nanometers."""
        return float(self.read("laser1:ctl:wavelength-act"))

    @wavelength.setter
    def wavelength(self, value):
        self.write(f"laser1:ctl:wavelength-set {value}")

    @property
    def current_setpoint(self):
        """Control the laser diode current setpoint in milliamperes."""
        return float(self.read("laser1:dl:cc:current-set"))

    @current_setpoint.setter
    def current_setpoint(self, value):
        self.write(f"laser1:dl:cc:current-set {value}")

    @property
    def temperature_setpoint(self):
        """Control the laser diode temperature setpoint in degrees Celsius."""
        return float(self.read("laser1:dl:tc:temp-set"))

    @temperature_setpoint.setter
    def temperature_setpoint(self, value):
        self.write(f"laser1:dl:tc:temp-set {value}")

    @property
    def system_label(self):
        """Control the user-defined system label."""
        return str(self.read("system-label"))

    @system_label.setter
    def system_label(self, value):
        self.write(f"system-label {value}")
    
    def setup_sweep(self, start_freq, stop_freq, scan_speed, output_channel='piezo'):
        """
        Configure frequency sweep parameters.
        
        Args:
            start_freq (float): Start frequency in THz
            stop_freq (float): Stop frequency in THz  
            scan_speed (float): Scan speed in THz/s
            output_channel (str): Output channel ('piezo' or 'current')
        """
        # Convert frequency to wavelength (λ = c/f, where c = 299.792458 THz·nm)
        c = 299.792458  # Speed of light in THz·nm
        start_wavelength = c / start_freq
        stop_wavelength = c / stop_freq
        
        # Map output channel to DeCoP value
        channel_map = {
            'piezo': 'PC',
            'current': 'CC'
        }
        
        if output_channel not in channel_map:
            raise ValueError(f"Invalid output channel '{output_channel}'. Must be 'piezo' or 'current'.")
        
        # Configure scan parameters
        self.write(f"laser1:scan:start {start_wavelength}")
        self.write(f"laser1:scan:end {stop_wavelength}")
        self.write(f"laser1:scan:frequency {scan_speed}")
        self.write(f"laser1:scan:output-channel {channel_map[output_channel]}")
    
    def start_sweep(self):
        """Start the configured frequency sweep."""
        self.write("laser1:scan:enabled true")
    
    def stop_sweep(self):
        """Stop the current frequency sweep."""
        self.write("laser1:scan:enabled false")
    
    def check_errors(self):
        """
        Check system health and raise error if not normal.
        
        This method is automatically called by PyMeasure after each operation.
        """
        try:
            health = self.system_health
            if health.lower() not in ['ok', 'nominal', 'normal']:
                raise ConnectionError(f"Laser system health error: {health}")
        except Exception:
            # Don't raise errors during error checking to avoid infinite loops
            pass
    
    def shutdown(self):
        """
        Safely shutdown the laser connection.
        
        This method disables laser emission and closes the connection.
        """
        try:
            # Disable laser emission for safety
            self.emission_enabled = False
            time.sleep(0.1)  # Allow time for emission to turn off
        except Exception:
            pass  # Continue shutdown even if emission control fails
        
        try:
            # Close the Toptica SDK client connection
            self.client.close()
        except Exception:
            pass  # Continue shutdown even if close fails
        
        # Call parent shutdown
        super().shutdown()
    
    @property
    def frequency(self):
        """Get the current laser frequency in THz."""
        wavelength_nm = self.wavelength_actual
        c = 299.792458  # Speed of light in THz·nm
        return c / wavelength_nm
    
    @frequency.setter
    def frequency(self, freq_thz):
        """Set the laser frequency in THz."""
        c = 299.792458  # Speed of light in THz·nm
        wavelength_nm = c / freq_thz*1000
        self.wavelength = wavelength_nm

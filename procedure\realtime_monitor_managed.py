"""
Realtime Monitor (ManagedWindow 版本)

用法（没有仪器也可运行）：
1) 安装依赖（至少）：
   - pip install pymeasure PyQt5
   - 可选：pip install pyqtgraph seabreeze
2) 运行：
   - python procedure/realtime_monitor_managed.py
3) 界面说明：
   - 顶部工具栏使用 PyMeasure 默认的开始/停止/队列按钮来启动/停止后台 Procedure。
   - 左侧 Status 显示 4 个指示灯（绿=连接成功，红=未连接）。
   - 右侧 Plots 显示光谱（Intensity vs Wavelength）与示波器（Voltage vs Time），若未安装 pyqtgraph 则展示提示。
   - 底部 Controls 可实时修改光谱积分时间(ms)、示波器时基(s/div)、电源通道/电压/限流/输出开关。修改立即经 CONTROL_STATE 同步到 Procedure。

为什么这个版本没那么直接：
- ManagedWindow 的 inputs 在运行后常被禁用，且采集运行在后台线程。要实现“运行时可改参数、实时显示”，需要：
  - 线程安全的“控制状态字典”（CONTROL_STATE）从 GUI 传递到 Procedure；
  - 队列（TELEMETRY_QUEUE）从 Procedure 把遥测推送到 GUI；
  - GUI 定时器（QTimer）持续刷新图表/状态。
- 优势：与 PyMeasure 的队列、日志、实验流程更好集成，便于记录与复用。

如何添加新的“输入参数”（两类）：
1) 运行前固定的参数（使用 PyMeasure Parameter，如连接地址）：
   - 在 RealtimeProcedure 中新增 Parameter/FloatParameter/IntegerParameter 等；
   - 在 RealtimeManagedWindow 构造时，将参数名添加到 inputs 与 displays 列表；
   - 在 Procedure.startup()/execute() 中使用 self.<param> 读取。
2) 运行时可实时调整的参数（例如示波器耦合方式）：
   - 在顶部 CONTROL_STATE 中新增键值默认；
   - 在 Controls Dock 中新增相应控件，连接 valueChanged/toggled 到 _update_control(key, value)；
   - 在 Procedure._apply_controls 中读取 CONTROL_STATE 并下发到设备。

如何添加新的“输出显示”：
- 在 Procedure.execute 中组装更多遥测键值（如温度、激光功率），通过 _put_telemetry 推送；
- 在 _pull_telemetry 中解析这些键值，更新新的曲线/标签；
- 若需要保存为结果文件，也可在 execute 中 self.emit('results', data) 并在 DATA_COLUMNS 中加入列名。
"""

from __future__ import annotations

import sys
import time
from typing import Optional
from queue import Queue, Empty
from threading import Lock

import numpy as np
from pymeasure.experiment import (
    Procedure, FloatParameter, IntegerParameter, Parameter, BooleanParameter,
    Results, Experiment, unique_filename,
)
from pymeasure.display.windows import ManagedWindow

from PyQt5.QtCore import QTimer
from PyQt5.QtWidgets import QLabel, QDockWidget, QWidget, QVBoxLayout, QHBoxLayout

try:
    import pyqtgraph as pg
    _HAS_PG = True
except Exception:
    _HAS_PG = False

# 仪器驱动
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from instrument.toptica_ctl import TopticaCTL
from instrument.siglent_spd import SiglentSPD4000X
from instrument.sds6108_h10_pro import SDS6108H10Pro


# ---------------------- 线程间通信对象 ----------------------
TELEMETRY_QUEUE: "Queue[dict]" = Queue(maxsize=3)
CONTROL_LOCK = Lock()
CONTROL_STATE = {
    "spec_integration_ms": 100,
    "scope_timebase_sdiv": 1e-3,
    "psu_channel": 1,
    "psu_voltage_v": 5.0,
    "psu_current_a": 1.0,
    "psu_output": False,
}


class RealtimeProcedure(Procedure):
    # 连接参数
    laser_ip = Parameter("Laser IP Address", default="************")
    psu_ip = Parameter("Power Supply IP Address", default="************")
    scope_ip = Parameter("Scope IP Address", default="************")
    refresh_rate_hz = IntegerParameter("Refresh Rate", default=5, minimum=1, maximum=50)

    # 兼容 ManagedWindow 的 PlotWidget 初始化（即使我们不使用它，也需提供至少一列）
    DATA_COLUMNS = ["Time (s)", "Value"]

    def startup(self):
        self.laser: Optional[TopticaCTL] = None
        self.psu: Optional[SiglentSPD4000X] = None
        self.psu_ch = None
        self.scope: Optional[SDS6108H10Pro] = None
        self.spec = None

        # 连接设备
        try:
            self.laser = TopticaCTL(self.laser_ip)
            laser_ok = True
            laser_sn = self.laser.serial_number
        except Exception:
            laser_ok = False
            laser_sn = "-"

        try:
            self.psu = SiglentSPD4000X(f"TCPIP::{self.psu_ip}::INSTR")
            with CONTROL_LOCK:
                ch = int(CONTROL_STATE["psu_channel"]) or 1
                vset = float(CONTROL_STATE["psu_voltage_v"]) 
                iset = float(CONTROL_STATE["psu_current_a"]) 
                out = bool(CONTROL_STATE["psu_output"]) 
            self.psu_ch = self.psu.channels[ch]
            self.psu_ch.current_limit = iset
            self.psu_ch.voltage = vset
            self.psu_ch.output_enabled = out
            psu_ok = True
        except Exception:
            psu_ok = False

        try:
            visa = f"TCPIP::{self.scope_ip}::INSTR"
            self.scope = SDS6108H10Pro(visa)
            with CONTROL_LOCK:
                tb = float(CONTROL_STATE["scope_timebase_sdiv"]) 
            self.scope.timebase_scale = tb
            # 确保 C1 可见
            try:
                self.scope.channels[1].display = True
            except Exception:
                pass
            scope_ok = True
            scope_idn = self.scope.idn
        except Exception:
            scope_ok = False
            scope_idn = "-"

        try:
            import seabreeze
            seabreeze.use('cseabreeze')
            import seabreeze.spectrometers as sb
            self.spec = sb.Spectrometer.from_first_available()
            with CONTROL_LOCK:
                integ_ms = int(CONTROL_STATE["spec_integration_ms"]) 
            self.spec.integration_time_micros(integ_ms * 1000)
            spec_ok = True
        except Exception:
            spec_ok = False

        # 初次发状态
        self._put_telemetry({
            "status": {
                "laser": laser_ok,
                "laser_sn": laser_sn,
                "psu": psu_ok,
                "scope": scope_ok,
                "scope_idn": scope_idn,
                "spec": spec_ok,
            }
        })

    def execute(self):
        dt = 1.0 / max(1, int(self.refresh_rate_hz))
        while not self.should_stop():
            # 应用 GUI 控制改动
            self._apply_controls()

            # 采集数据
            wl = it = None
            t = v = None
            psu_v = psu_i = None
            try:
                if self.spec is not None:
                    wl = self.spec.wavelengths()
                    it = self.spec.intensities()
            except Exception:
                pass
            try:
                if self.scope is not None:
                    t, v = self.scope.fetch_waveform_screen(1)
            except Exception:
                pass
            try:
                if self.psu_ch is not None:
                    psu_v = self.psu_ch.measure_voltage
                    psu_i = self.psu_ch.measure_current
            except Exception:
                pass

            # 推送遥测
            self._put_telemetry({
                "spectrometer": {
                    "wavelengths": wl.tolist() if isinstance(wl, np.ndarray) else (wl if wl is None else list(wl)),
                    "intensities": it.tolist() if isinstance(it, np.ndarray) else (it if it is None else list(it)),
                },
                "scope": {
                    "time": t.tolist() if isinstance(t, np.ndarray) else (t if t is None else list(t)),
                    "voltage": v.tolist() if isinstance(v, np.ndarray) else (v if v is None else list(v)),
                },
                "psu": {
                    "voltage": psu_v,
                    "current": psu_i,
                }
            })

            time.sleep(dt)

    def _apply_controls(self):
        # 从 CONTROL_STATE 读取并下发到设备
        with CONTROL_LOCK:
            integ_ms = int(CONTROL_STATE["spec_integration_ms"]) 
            tb = float(CONTROL_STATE["scope_timebase_sdiv"]) 
            ch = int(CONTROL_STATE["psu_channel"]) 
            vset = float(CONTROL_STATE["psu_voltage_v"]) 
            iset = float(CONTROL_STATE["psu_current_a"]) 
            out = bool(CONTROL_STATE["psu_output"]) 

        try:
            if self.spec is not None:
                self.spec.integration_time_micros(integ_ms * 1000)
        except Exception:
            pass

        try:
            if self.scope is not None:
                self.scope.timebase_scale = tb
        except Exception:
            pass

        try:
            if self.psu is not None:
                # 若通道改变则切换
                if self.psu_ch is None or self.psu_ch.id != ch:
                    self.psu_ch = self.psu.channels[ch]
                self.psu_ch.current_limit = iset
                self.psu_ch.voltage = vset
                self.psu_ch.output_enabled = out
        except Exception:
            pass

    def shutdown(self):
        try:
            if self.spec is not None:
                self.spec.close()
        except Exception:
            pass
        try:
            if self.scope is not None:
                self.scope.shutdown()
        except Exception:
            pass
        try:
            if self.psu is not None:
                self.psu.shutdown()
        except Exception:
            pass
        try:
            if self.laser is not None:
                self.laser.shutdown()
        except Exception:
            pass

    # 安全入队（丢弃旧帧）
    def _put_telemetry(self, payload: dict):
        try:
            while not TELEMETRY_QUEUE.empty():
                TELEMETRY_QUEUE.get_nowait()
        except Exception:
            pass
        try:
            TELEMETRY_QUEUE.put_nowait(payload)
        except Exception:
            pass


class RealtimeManagedWindow(ManagedWindow):
    def __init__(self):
        super().__init__(
            procedure_class=RealtimeProcedure,
            inputs=[
                "laser_ip",
                "psu_ip",
                "scope_ip",
                "refresh_rate_hz",
            ],
            displays=[
                "laser_ip",
                "psu_ip",
                "scope_ip",
                "refresh_rate_hz",
            ],
            x_axis=None,
            y_axis=None,
        )
        self.setWindowTitle("Realtime Instrument Monitor (ManagedWindow)")

        # 指示灯 Dock
        leds_dock = QDockWidget("Status", self)
        leds_widget = QWidget(); leds_layout = QVBoxLayout(leds_widget); leds_layout.setContentsMargins(6,6,6,6)
        self.led_laser = QLabel(); self.led_scope = QLabel(); self.led_psu = QLabel(); self.led_spec = QLabel()
        for led in (self.led_laser, self.led_scope, self.led_psu, self.led_spec):
            led.setFixedSize(14, 14)
            led.setStyleSheet("border-radius:7px; background:#999;")
        leds_layout.addWidget(self._label_with_led("Laser", self.led_laser))
        leds_layout.addWidget(self._label_with_led("Scope", self.led_scope))
        leds_layout.addWidget(self._label_with_led("PSU", self.led_psu))
        leds_layout.addWidget(self._label_with_led("Spec", self.led_spec))
        leds_layout.addStretch(1)
        leds_dock.setWidget(leds_widget)
        self.addDockWidget(0x1, leds_dock)  # LeftDockWidgetArea

        # 绘图 Dock
        plots_dock = QDockWidget("Plots", self)
        plots_widget = QWidget(); plots_layout = QHBoxLayout(plots_widget); plots_layout.setContentsMargins(6,6,6,6)
        if _HAS_PG:
            self.pg_spec = pg.PlotWidget(title="Spectrometer")
            self.pg_spec.setLabel('bottom', 'Wavelength', units='nm')
            self.pg_spec.setLabel('left', 'Intensity', units='arb.')
            self.curve_spec = self.pg_spec.plot(pen='y')
            self.pg_scope = pg.PlotWidget(title="Oscilloscope")
            self.pg_scope.setLabel('bottom', 'Time', units='s')
            self.pg_scope.setLabel('left', 'Voltage', units='V')
            self.curve_scope = self.pg_scope.plot(pen='c')
            plots_layout.addWidget(self.pg_spec)
            plots_layout.addWidget(self.pg_scope)
        else:
            plots_layout.addWidget(QLabel("pyqtgraph 未安装，无法显示曲线。"))
        plots_dock.setWidget(plots_widget)
        self.addDockWidget(0x2, plots_dock)  # RightDockWidgetArea

        # 控制 Dock
        from PyQt5.QtWidgets import QFormLayout, QSpinBox, QDoubleSpinBox, QCheckBox
        ctrl_dock = QDockWidget("Controls", self)
        form = QWidget(); fl = QFormLayout(form)
        self.sb_spec_ms = QSpinBox(); self.sb_spec_ms.setRange(1, 10000); self.sb_spec_ms.setValue(CONTROL_STATE["spec_integration_ms"]) ; self.sb_spec_ms.setSuffix(" ms")
        self.dsb_tb = QDoubleSpinBox(); self.dsb_tb.setDecimals(9); self.dsb_tb.setRange(1e-9, 1000.0); self.dsb_tb.setValue(CONTROL_STATE["scope_timebase_sdiv"]) ; self.dsb_tb.setSuffix(" s/div")
        self.sb_psu_ch = QSpinBox(); self.sb_psu_ch.setRange(1, 4); self.sb_psu_ch.setValue(CONTROL_STATE["psu_channel"]) 
        self.dsb_psu_v = QDoubleSpinBox(); self.dsb_psu_v.setDecimals(3); self.dsb_psu_v.setRange(0.0, 20.0); self.dsb_psu_v.setValue(CONTROL_STATE["psu_voltage_v"]) ; self.dsb_psu_v.setSuffix(" V")
        self.dsb_psu_i = QDoubleSpinBox(); self.dsb_psu_i.setDecimals(3); self.dsb_psu_i.setRange(0.0, 10.0); self.dsb_psu_i.setValue(CONTROL_STATE["psu_current_a"]) ; self.dsb_psu_i.setSuffix(" A")
        self.cb_psu_out = QCheckBox("PSU 输出开"); self.cb_psu_out.setChecked(CONTROL_STATE["psu_output"]) 
        fl.addRow("Spec Integration", self.sb_spec_ms)
        fl.addRow("Scope Timebase", self.dsb_tb)
        fl.addRow("PSU Channel", self.sb_psu_ch)
        fl.addRow("PSU Voltage", self.dsb_psu_v)
        fl.addRow("PSU Current", self.dsb_psu_i)
        fl.addRow(self.cb_psu_out)
        ctrl_dock.setWidget(form)
        self.addDockWidget(0x8, ctrl_dock)  # BottomDockWidgetArea

        # 连接控件信号到 CONTROL_STATE
        self.sb_spec_ms.valueChanged.connect(lambda v: self._update_control("spec_integration_ms", int(v)))
        self.dsb_tb.valueChanged.connect(lambda v: self._update_control("scope_timebase_sdiv", float(v)))
        self.sb_psu_ch.valueChanged.connect(lambda v: self._update_control("psu_channel", int(v)))
        self.dsb_psu_v.valueChanged.connect(lambda v: self._update_control("psu_voltage_v", float(v)))
        self.dsb_psu_i.valueChanged.connect(lambda v: self._update_control("psu_current_a", float(v)))
        self.cb_psu_out.toggled.connect(lambda v: self._update_control("psu_output", bool(v)))

        # 定时器：从队列取遥测更新界面
        self.timer = QTimer(self)
        self.timer.setInterval(200)
        self.timer.timeout.connect(self._pull_telemetry)
        self.timer.start()

    def queue(self):
        """将当前参数组装为一次实验并入队运行。

        注意：我们主要用于实时显示，不强调数据文件；但为兼容 Manager，这里仍创建 Results/Experiment。
        """
        procedure = self.make_procedure()
        # 生成临时文件名（即使不写数据也可兼容）
        try:
            filename = unique_filename("results", prefix="realtime", ext="csv")
        except Exception:
            import os, time as _t
            os.makedirs("results", exist_ok=True)
            filename = os.path.join("results", f"realtime_{int(_t.time())}.csv")
        results = Results(procedure, filename)
        experiment = Experiment(results, procedure)
        self.manager.queue(experiment)

    # 辅助：指示灯 + 标签容器
    def _label_with_led(self, name: str, led: QLabel):
        w = QWidget()
        ly = QHBoxLayout(w)
        ly.setContentsMargins(0, 0, 0, 0)
        ly.addWidget(led)
        ly.addWidget(QLabel(name))
        return w

    def _set_led(self, led: QLabel, ok: bool):
        led.setStyleSheet(f"border-radius:7px; background:{'#2ecc71' if ok else '#e74c3c'};")

    def _update_control(self, key: str, value):
        with CONTROL_LOCK:
            CONTROL_STATE[key] = value

    def _pull_telemetry(self):
        try:
            payload = TELEMETRY_QUEUE.get_nowait()
        except Empty:
            return
        # 状态灯
        status = payload.get("status")
        if status:
            self._set_led(self.led_laser, bool(status.get("laser")))
            self._set_led(self.led_scope, bool(status.get("scope")))
            self._set_led(self.led_psu, bool(status.get("psu")))
            self._set_led(self.led_spec, bool(status.get("spec")))
        # 光谱
        if _HAS_PG:
            spec = payload.get("spectrometer") or {}
            wl = spec.get("wavelengths")
            it = spec.get("intensities")
            if wl and it:
                self.curve_spec.setData(wl, it)
            sc = payload.get("scope") or {}
            tt = sc.get("time")
            vv = sc.get("voltage")
            if tt and vv:
                self.curve_scope.setData(tt, vv)
        # 电源：可在窗口状态栏显示或扩展 label，这里略


if __name__ == "__main__":
    # 兼容无 pyqtgraph 环境
    if _HAS_PG:
        app = pg.mkQApp()
    else:
        from PyQt5.QtWidgets import QApplication
        app = QApplication(sys.argv)
    win = RealtimeManagedWindow()
    win.show()
    sys.exit(app.exec_())



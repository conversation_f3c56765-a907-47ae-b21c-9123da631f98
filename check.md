08/08/2025 12:18:13 AM : ManagedWindow connected to logging (INFO)
08/08/2025 12:18:19 AM : Worker thread started (INFO)
08/08/2025 12:18:19 AM : Worker started running an instance of 'FrequencySweepProcedure' (INFO)
08/08/2025 12:18:19 AM : Starting frequency sweep measurement... (INFO)
08/08/2025 12:18:19 AM : Connecting to power supply at 192.168.3.10 (INFO)
08/08/2025 12:18:19 AM : Initializing Siglent SPD4000X Power Supply. (INFO)
08/08/2025 12:18:19 AM : Connected to power supply (INFO)
08/08/2025 12:18:19 AM : Connecting to spectrometer... (INFO)
08/08/2025 12:18:19 AM : Connected to spectrometer (INFO)
08/08/2025 12:18:19 AM : Setting power supply CH1 to 5.0V (INFO)
08/08/2025 12:18:19 AM : Worker caught an error on (ERROR)
  Traceback (most recent call last):
    File "D:\measure\.venv\Lib\site-packages\pymeasure\experiment\workers.py", line 177, in run
      self.procedure.startup()
    File "d:\measure\procedure\frequency_sweep_procedure - 副本.py", line 175, in startup
      self.channel.voltage = self.voltage_setpoint
      ^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pymeasure\instruments\common_base.py", line 287, in __setattr__
      super().__setattr__(name, value)
    File "d:\measure\instrument\siglent_spd.py", line 67, in voltage
      current_voltage = self.voltage
                        ^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pymeasure\instruments\common_base.py", line 298, in __getattribute__
      return super().__getattribute__(name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "d:\measure\instrument\siglent_spd.py", line 51, in voltage
      return float(self.ask(f"CH{self.id}:VOLTage?"))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pymeasure\instruments\common_base.py", line 387, in ask
      return self.read()
             ^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pymeasure\instruments\channel.py", line 79, in read
      return self.parent.read(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pymeasure\instruments\instrument.py", line 173, in read
      return self.adapter.read(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pymeasure\adapters\adapter.py", line 108, in read
      read = self._read(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pymeasure\adapters\visa.py", line 172, in _read
      return self.connection.read(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pyvisa\resources\messagebased.py", line 519, in read
      message = self._read_raw().decode(enco)
                ^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pyvisa\resources\messagebased.py", line 473, in _read_raw
      chunk, status = self.visalib.read(self.session, size)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pyvisa\ctwrapper\functions.py", line 2339, in read
      ret = library.viRead(session, buffer, count, byref(return_count))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pyvisa\ctwrapper\highlevel.py", line 227, in _return_handler
      return self.handle_return_value(session, ret_value)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pyvisa\highlevel.py", line 252, in handle_return_value
      raise errors.VisaIOError(rv)
  pyvisa.errors.VisaIOError: VI_ERROR_TMO (-1073807339): Timeout expired before operation completed.
08/08/2025 12:18:19 AM : Shutting down instruments... (INFO)
08/08/2025 12:18:19 AM : Shutting down power supply (INFO)
08/08/2025 12:18:19 AM : Error shutting down power supply: VI_ERROR_TMO (-1073807339): Timeout expired before operation completed. (ERROR)
08/08/2025 12:18:19 AM : Closing spectrometer (INFO)
08/08/2025 12:18:19 AM : Shutdown complete (INFO)
08/08/2025 12:18:19 AM : Monitor caught stop command (INFO)
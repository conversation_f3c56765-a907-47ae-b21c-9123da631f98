08/08/2025 12:48:38 AM : ManagedWindow connected to logging (INFO)
08/08/2025 12:48:40 AM : Worker thread started (INFO)
08/08/2025 12:48:40 AM : Worker started running an instance of 'FrequencySweepProcedure' (INFO)
08/08/2025 12:48:40 AM : Starting frequency sweep measurement... (INFO)
08/08/2025 12:48:40 AM : Connecting to Toptica laser at 192.168.3.34 (INFO)
08/08/2025 12:48:40 AM : Initializing Toptica CTL Laser. (INFO)
08/08/2025 12:48:40 AM : Connected to laser: DLC PRO_047256 (INFO)
08/08/2025 12:48:40 AM : Connecting to power supply at 192.168.3.10 (INFO)
08/08/2025 12:48:40 AM : Initializing Siglent SPD4000X Power Supply. (INFO)
08/08/2025 12:48:40 AM : Connected to power supply (INFO)
08/08/2025 12:48:40 AM : Connecting to spectrometer... (INFO)
08/08/2025 12:48:40 AM : Connected to spectrometer (INFO)
08/08/2025 12:48:40 AM : Setting power supply CH1 to 5.0V (INFO)
08/08/2025 12:48:41 AM : Enabling laser emission (INFO)
08/08/2025 12:48:41 AM : Worker caught an error on (ERROR)
  Traceback (most recent call last):
    File "d:\measure\instrument\toptica_ctl.py", line 138, in write
      self.client.set(param, value)
    File "D:\measure\.venv\Lib\site-packages\toptica\lasersdk\client.py", line 327, in set
      return cast(int, self._async_run(self._async_client.set(param_name, *param_values)))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\toptica\lasersdk\client.py", line 508, in _async_run
      return future.result()
             ^^^^^^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\_base.py", line 456, in result
      return self.__get_result()
             ^^^^^^^^^^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\_base.py", line 401, in __get_result
      raise self._exception
    File "D:\measure\.venv\Lib\site-packages\toptica\lasersdk\client.py", line 502, in _run_with_timeout
      return await asyncio.wait_for(_coro, self._connection.timeout)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py", line 520, in wait_for
      return await fut
             ^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\toptica\lasersdk\asyncio\client.py", line 295, in set
      status = cast(int, decode_value(result, int))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\toptica\lasersdk\decop.py", line 215, in decode_value
      raise DecopError(result)
  toptica.lasersdk.decop.DecopError: Error: -11 parameter not settable
  
  During handling of the above exception, another exception occurred:
  
  Traceback (most recent call last):
    File "D:\measure\.venv\Lib\site-packages\pymeasure\experiment\workers.py", line 177, in run
      self.procedure.startup()
    File "d:\measure\procedure\frequency_sweep_procedure.py", line 185, in startup
      self.laser.emission_enabled = True
      ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "D:\measure\.venv\Lib\site-packages\pymeasure\instruments\common_base.py", line 287, in __setattr__
      super().__setattr__(name, value)
    File "d:\measure\instrument\toptica_ctl.py", line 185, in emission_enabled
      self.write(f"laser1:emission {'true' if value else 'false'}")
    File "d:\measure\instrument\toptica_ctl.py", line 141, in write
      raise ConnectionError(f"Failed to write command '{command}': {e}")
  ConnectionError: Failed to write command 'laser1:emission true': Error: -11 parameter not settable
08/08/2025 12:48:41 AM : Shutting down instruments... (INFO)
08/08/2025 12:48:41 AM : Disabling laser emission (INFO)
08/08/2025 12:48:41 AM : Error shutting down laser: Failed to write command 'laser1:emission false': Error: -11 parameter not settable (ERROR)
08/08/2025 12:48:41 AM : Shutting down power supply (INFO)
08/08/2025 12:48:42 AM : Closing spectrometer (INFO)
08/08/2025 12:48:42 AM : Shutdown complete (INFO)
08/08/2025 12:48:42 AM : Monitor caught stop command (INFO)
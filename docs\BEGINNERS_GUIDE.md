# 实验室自动化与实时监测程序：入门指南

本指南面向初学者，讲解本仓库（lab_framework）内两个常用程序的编写思路与扩展方法：
- 频率扫描实验（procedure/frequency_sweep_procedure.py）
- 实时监测界面（procedure/realtime_monitor.py）

并介绍所用第三方库（PyMeasure、PyQt5、pyqtgraph、SeaBreeze、pyvisa/pyvisa-py、TOPTICA Laser SDK）的基本用法、选择理由与优点，帮助你快速理解程序结构并进行二次开发。

---

## 一、总体架构与技术选型

- PyMeasure：科研测量自动化框架，提供“实验流程（Procedure）—图形界面（ManagedWindow）—数据记录（Results）—并发执行（Worker）”等高层抽象。
  - 优点：
    - 标准化的参数定义（FloatParameter/IntegerParameter/Parameter）和数据列（DATA_COLUMNS）。
    - 跨平台 GUI 与任务管理（无需手写线程与日志管理）。
    - 与“仪器驱动”解耦，便于复用与扩展。
- PyQt5：图形界面库。PyMeasure 自带的窗口满足通用流程；realtime_monitor 自定义了更灵活的窗口与控件布局。
- pyqtgraph：高效绘图（波形/光谱）库，适合实时刷新，API 简洁，性能优于纯 matplotlib。
- SeaBreeze（cseabreeze/pyseabreeze）：海洋光学光谱仪驱动库。
- pyvisa / pyvisa-py：VISA 通信层，用于以 TCPIP/USB/GPIB 访问 Siglent 电源、示波器等设备。
- TOPTICA Laser SDK（toptica-lasersdk）：Toptica CTL + DLC Pro 的官方 Python SDK。

依赖清单见 `requirements.txt`。

---

## 二、频率扫描程序（frequency_sweep_procedure.py）编写思路

文件：`procedure/frequency_sweep_procedure.py`

1) 使用 PyMeasure 的 Procedure 抽象：
- 定义实验“输入参数”：如电源电压、电源通道、频率起止、步数、光谱积分时间等（FloatParameter、IntegerParameter、Parameter）。
- 定义数据列 DATA_COLUMNS：规范每个数据点的输出字段（频率、波长、峰值强度、电源读数等）。

2) 在 startup() 中完成设备初始化：
- 连接电源（SiglentSPD4000X）并选择通道，设置电流上限、电压、并开启输出（output_enabled）。
- 连接光谱仪（SeaBreeze），设置积分时间。
- 连接激光器（TopticaCTL），检查系统健康，打开激光发射（emission_enabled=True）。

3) 在 execute() 中执行核心扫描：
- 用 numpy.linspace 生成频率序列，逐点：
  - 写入激光目标频率（laser.frequency = target_frequency）。
  - 等待稳定时间（settling_time）。
  - 读取实际波长/频率、电源电压/电流、采集光谱（wavelengths()/intensities()）。
  - 提取峰值与对应波长，emit('results', data) 输出结果，emit('progress', 百分比) 更新进度。

4) 在 shutdown() 中安全关闭设备：
- 关闭激光发射并释放连接。
- 电源渐降电压到 0V 并关闭输出（驱动已支持平滑过渡）。
- 关闭光谱仪。

5) GUI：
- 使用 PyMeasure 的 ManagedWindow（FrequencySweepWindow）将 Procedure 参数自动渲染为表单，x/y 轴选择“频率/强度”。

为什么选 PyMeasure：
- 将“实验参数—执行—记录—界面—线程”一次性打通，极大简化样板代码。
- 扩展性强：新增参数、数据列和采集逻辑都很直观。

---

## 三、仪器驱动的设计要点

- Siglent 电源（`instrument/siglent_spd.py`）
  - 基于 PyMeasure 的 Instrument 与 Channel；四通道电源按 CH1..CH4 建模，电压/电流范围按官方规格区分。
  - 关键属性：
    - `channel.voltage`：支持大幅变化时的“平滑过渡”（分步设置+延时），避免对负载冲击。
    - `channel.current_limit`、`channel.output_enabled`、`channel.measure_voltage`、`channel.measure_current`。
  - 常见坑：不要把复杂的“设置过程函数”直接塞进 Instrument.control 的 `set_process` 回调，否则可能在某些框架版本中触发签名/参数传递问题。这里我们改为标准的 Python property，内部实现平滑设定逻辑，清晰可靠。

- Toptica CTL（`instrument/toptica_ctl.py`）
  - 直接使用 Toptica SDK（Client.set/get/exec）实现 read/write；对外提供 `wavelength` / `frequency` / `emission_enabled` 等属性。
  - `frequency` 与 `wavelength` 互相换算（c=299.792458 THz·nm）；设置频率时换算为波长后写入。
  - `setup_sweep()/start_sweep()/stop_sweep()` 提供硬件扫频配置与控制（按 DeCoP 参数映射）。

- 示波器（`instrument/sds6108_h10_pro.py`）
  - 暂略，此驱动提供 `timebase_scale`、`channels[n].display`、`fetch_waveform_screen(n)`、`screenshot_png(path)` 等方法，供实时程序使用。

---

## 四、实时监测程序（realtime_monitor.py）编写思路

文件：`procedure/realtime_monitor.py`

目标：在一个窗口内实时查看/控制激光器、示波器、电源与光谱仪，并支持保存示波器截图与波形 CSV。

1) UI 结构
- 右侧 Tab：
  - 连接：显示当前默认 IP；一键连接/断开。
  - 控制：
    - 光谱仪：积分时间（ms）。
    - 示波器：时基 s/div；选择显示/保存的通道 C1..C4（多选）。
    - 电源：通道选择、电压/限流、输出开关。
  - 状态/保存：显示连接状态、电源读数（V/I）、保存按钮（截图/波形）。
- 左侧：两个 pyqtgraph 图：
  - Oscilloscope：多通道电压-时间曲线，支持多选通道实时刷新。
  - Spectrometer：强度-波长曲线实时刷新。

2) 线程与轮询
- 启动后台线程 `_poll_loop` 每 0.2 s 刷新：
  - 若有光谱仪：更新光谱曲线。
  - 若有示波器：读被选通道的屏幕等效波形，更新/清空对应曲线。
  - 若有电源通道：读测量值并更新标签。
- UI 控件的 valueChanged/toggled 信号直连回调，直接写入仪器属性，即时生效。

3) 保存功能
- 截图：调用 `scope.screenshot_png(path)`。
- 波形：对所选 Cn 逐一 `fetch_waveform_screen(n)`，保存 CSV（time_s, voltage_v）。

为什么自写 PyQt + pyqtgraph：
- 需要更细粒度的“实时绘图控制、布局控制、交互控件”，而不是标准 Procedure 窗口。
- pyqtgraph 性能更适合高频率刷新与多曲线场景，API 上手快。

---

## 五、如何扩展（面向初学者的改造路径）

1) 新增一个输入控件（例如：示波器垂直刻度/耦合/带宽限制）
- 在“控制”组中添加控件（QComboBox/QDoubleSpinBox 等）。
- 连接 valueChanged/toggled 信号到回调，例如 `_on_scope_vscale_changed`。
- 在回调中写驱动属性：`self._scope.channels[1].scale = 值`。

2) 新增一个输出显示（例如：激光器实际功率/温度曲线）
- 在“状态/保存”组或左侧图区域增加标签/曲线。
- 在 `_poll_loop` 内读取 `laser.power_actual` 或 `temperature_actual` 并 `setText` 或 `setData`。

3) 保存更多数据
- 电源长期日志：在 `_poll_loop` 中把 `measure_voltage/current` 追加写入 CSV，带时间戳。
- 光谱快照：新增按钮“保存光谱”，点击时 `wavelengths()/intensities()` 保存 CSV。

4) 兼容无硬件环境
- 将各仪器 driver 加上“模拟模式”参数或创建 `Mock*` 驱动，随机/波形生成数据，方便开发与调试。
- UI 保持可启动，连接失败时给出友好提示，不影响其它设备功能。

5) 常见易错与建议
- 线程安全：尽量使用库已保证线程安全的绘图更新接口；复杂场景可使用 Qt 信号把数据投递到主线程。
- 通信稳定性：加入超时与异常捕获，失败重试；断开/关闭要兜底（try/except）。
- 单位换算：频率/波长、时间单位与采样点数要统一；注释清楚，避免混淆。
- 电源平滑设定：对大幅电压变化做分步过渡，避免对被测对象冲击（本驱动已内置）。

---

## 六、用户如何修改绘图与界面

- 修改曲线样式：
  - 在 `realtime_monitor.py` 中查找 `pg.mkPen('c', width=2)` 等；可改颜色、线宽、增加图例等。
  - 调整网格/字体：在创建 PlotWidget 处修改 `showGrid()`、axis 字体大小。
- 添加多图联动：
  - 用 `QSplitter` 已支持拖拽调整比例；如需联动缩放，可使用 pyqtgraph 的 `setXLink`/`setYLink`。
- 切换绘图库：
  - 希望用 matplotlib：可替换左侧绘图区域为 FigureCanvas，但实时性能会稍差，需优化 blitting。

示例（改曲线颜色/线宽）：
- 找到：
  - `self.curve_scope = self.pg_scope.plot(pen=pg.mkPen('c', width=2))`
  - `self.curve_spec = self.pg_spec.plot(pen=pg.mkPen('y', width=2))`
- 修改为：
  - `pg.mkPen('#00FF88', width=3)` 等。

---

## 七、如何运行

- 安装依赖：`pip install -r requirements.txt`
- 运行频率扫描：
  - `python procedure/frequency_sweep_procedure.py`
  - 出现 PyMeasure 窗口后，填写 IP、参数，点击“开始”。
- 运行实时监测：
  - `python procedure/realtime_monitor.py`
  - 右侧“连接”页点击“连接”，注意先在文件顶部配置默认 IP。

---

## 八、常见问题 FAQ

1) 连接失败（电源/示波器）
- 检查网络连通（ping IP），防火墙关闭或放行端口。
- 若未安装 NI-VISA，可先使用 `pyvisa-py`（纯 Python 后端），性能略低但易部署。

2) 光谱仪找不到设备
- 根据平台选择 `seabreeze.use('cseabreeze')`（C 扩展更快）或 `'pyseabreeze'`（纯 Python）。
- Windows 需 Zadig 绑定 USB 驱动；安装 `pyusb`。

3) 电源电压设置抛错（TypeError … missing 1 required positional argument: 'value'）
- 这是把电压“平滑设定函数”误用为 Instrument.control 的 `set_process` 导致的签名问题。本仓库已改为标准 property setter，问题已修复。

4) 频率/波长换算不对
- 确认使用 `c = 299.792458 THz·nm`；频率 THz -> 波长 nm：`λ = c / f`。

---

## 九、代码阅读顺序建议

- 先读 `procedure/realtime_monitor.py`：UI 与交互直观，易上手。
- 再读 `instrument/siglent_spd.py`、`instrument/toptica_ctl.py`：理解驱动接口（属性/方法）如何被 UI 调用。
- 最后读 `procedure/frequency_sweep_procedure.py`：把流程化、批量化采集串起来。

---

## 十、下一步可以做什么

- 为频率扫描加入自适应步长（根据光谱峰形调整）。
- 为实时监测加入记录功能（时间序列 CSV/Parquet）。
- 为所有仪器增加“重连”按钮与状态自检。
- 增加日志面板/文件滚动记录，便于排障。

如需更深入的帮助或新增设备支持，建议先在 docs/ 下新增设计草图与接口约定，再落地编码。
